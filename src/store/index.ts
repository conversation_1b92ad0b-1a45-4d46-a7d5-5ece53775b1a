import { create } from 'zustand';
import { User, CartItem, Product, Order, Notification, UserCoupon } from '../types';

// 用户状态
interface UserState {
  user: User | null;
  isLoggedIn: boolean;
  login: (user: User) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
}

export const useUserStore = create<UserState>((set) => ({
  user: null,
  isLoggedIn: false,
  login: (user) => set({ user, isLoggedIn: true }),
  logout: () => set({ user: null, isLoggedIn: false }),
  updateUser: (updates) => set((state) => ({
    user: state.user ? { ...state.user, ...updates } : null
  }))
}));

// 购物车状态
interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  addItem: (product: Product, quantity?: number, selectedSpecs?: { [specId: string]: string }) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  getItemCount: () => number;
  getTotalPrice: () => number;
}

export const useCartStore = create<CartState>((set, get) => ({
  items: [],
  totalItems: 0,
  totalPrice: 0,
  
  addItem: (product, quantity = 1, selectedSpecs = {}) => {
    const state = get();
    const existingItemIndex = state.items.findIndex(
      item => item.productId === product.id && 
      JSON.stringify(item.selectedSpecs) === JSON.stringify(selectedSpecs)
    );
    
    let newItems: CartItem[];
    
    if (existingItemIndex >= 0) {
      // 更新现有商品数量
      newItems = state.items.map((item, index) => 
        index === existingItemIndex 
          ? { ...item, quantity: item.quantity + quantity, totalPrice: (item.quantity + quantity) * item.price }
          : item
      );
    } else {
      // 添加新商品
      const newItem: CartItem = {
        id: `${product.id}_${Date.now()}`,
        productId: product.id,
        product,
        quantity,
        selectedSpecs,
        price: product.price,
        totalPrice: product.price * quantity
      };
      newItems = [...state.items, newItem];
    }
    
    const totalItems = newItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = newItems.reduce((sum, item) => sum + item.totalPrice, 0);
    
    set({ items: newItems, totalItems, totalPrice });
  },
  
  removeItem: (itemId) => {
    const state = get();
    const newItems = state.items.filter(item => item.id !== itemId);
    const totalItems = newItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = newItems.reduce((sum, item) => sum + item.totalPrice, 0);
    
    set({ items: newItems, totalItems, totalPrice });
  },
  
  updateQuantity: (itemId, quantity) => {
    const state = get();
    if (quantity <= 0) {
      get().removeItem(itemId);
      return;
    }
    
    const newItems = state.items.map(item => 
      item.id === itemId 
        ? { ...item, quantity, totalPrice: item.price * quantity }
        : item
    );
    
    const totalItems = newItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = newItems.reduce((sum, item) => sum + item.totalPrice, 0);
    
    set({ items: newItems, totalItems, totalPrice });
  },
  
  clearCart: () => set({ items: [], totalItems: 0, totalPrice: 0 }),
  
  getItemCount: () => {
    const state = get();
    return state.items.reduce((sum, item) => sum + item.quantity, 0);
  },
  
  getTotalPrice: () => {
    const state = get();
    return state.items.reduce((sum, item) => sum + item.totalPrice, 0);
  }
}));

// 订单状态
interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  addOrder: (order: Order) => void;
  updateOrder: (orderId: string, updates: Partial<Order>) => void;
  setCurrentOrder: (order: Order | null) => void;
  getOrderById: (orderId: string) => Order | undefined;
}

export const useOrderStore = create<OrderState>((set, get) => ({
  orders: [],
  currentOrder: null,
  
  addOrder: (order) => {
    const state = get();
    set({ orders: [order, ...state.orders] });
  },
  
  updateOrder: (orderId, updates) => {
    const state = get();
    const newOrders = state.orders.map(order => 
      order.id === orderId ? { ...order, ...updates } : order
    );
    set({ orders: newOrders });
    
    // 如果当前订单被更新，也更新当前订单
    if (state.currentOrder?.id === orderId) {
      set({ currentOrder: { ...state.currentOrder, ...updates } });
    }
  },
  
  setCurrentOrder: (order) => set({ currentOrder: order }),
  
  getOrderById: (orderId) => {
    const state = get();
    return state.orders.find(order => order.id === orderId);
  }
}));

// 通知状态
interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Notification) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  removeNotification: (notificationId: string) => void;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,
  
  addNotification: (notification) => {
    const state = get();
    const newNotifications = [notification, ...state.notifications];
    const unreadCount = newNotifications.filter(n => !n.isRead).length;
    set({ notifications: newNotifications, unreadCount });
  },
  
  markAsRead: (notificationId) => {
    const state = get();
    const newNotifications = state.notifications.map(notification => 
      notification.id === notificationId 
        ? { ...notification, isRead: true }
        : notification
    );
    const unreadCount = newNotifications.filter(n => !n.isRead).length;
    set({ notifications: newNotifications, unreadCount });
  },
  
  markAllAsRead: () => {
    const state = get();
    const newNotifications = state.notifications.map(notification => 
      ({ ...notification, isRead: true })
    );
    set({ notifications: newNotifications, unreadCount: 0 });
  },
  
  removeNotification: (notificationId) => {
    const state = get();
    const newNotifications = state.notifications.filter(n => n.id !== notificationId);
    const unreadCount = newNotifications.filter(n => !n.isRead).length;
    set({ notifications: newNotifications, unreadCount });
  }
}));

// 应用状态
interface AppState {
  currentRole: 'employee' | 'merchant' | 'enterprise_admin' | 'platform_admin' | null;
  isLoading: boolean;
  error: string | null;
  setCurrentRole: (role: 'employee' | 'merchant' | 'enterprise_admin' | 'platform_admin' | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useAppStore = create<AppState>((set) => ({
  currentRole: null,
  isLoading: false,
  error: null,
  setCurrentRole: (role) => set({ currentRole: role }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error })
}));

// 优惠券状态
interface CouponState {
  userCoupons: UserCoupon[];
  selectedCoupon: UserCoupon | null;
  addUserCoupon: (coupon: UserCoupon) => void;
  setSelectedCoupon: (coupon: UserCoupon | null) => void;
  useCoupon: (couponId: string, orderId: string) => void;
}

export const useCouponStore = create<CouponState>((set, get) => ({
  userCoupons: [],
  selectedCoupon: null,
  
  addUserCoupon: (coupon) => {
    const state = get();
    set({ userCoupons: [...state.userCoupons, coupon] });
  },
  
  setSelectedCoupon: (coupon) => set({ selectedCoupon: coupon }),
  
  useCoupon: (couponId, orderId) => {
    const state = get();
    const newUserCoupons = state.userCoupons.map(userCoupon => 
      userCoupon.id === couponId 
        ? { ...userCoupon, status: 'used' as const, usedAt: new Date().toISOString(), orderId }
        : userCoupon
    );
    set({ userCoupons: newUserCoupons, selectedCoupon: null });
  }
}));