// 用户角色类型
export type UserRole = 'employee' | 'merchant' | 'delivery' | 'enterprise_admin' | 'platform_admin' | 'region_manager';

// 用户信息
export interface User {
  id: string;
  name: string;
  phone: string;
  email?: string;
  role: UserRole;
  avatar?: string;
  enterpriseId?: string;
  merchantId?: string;
  regionId?: string;
}

// 商品信息
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  image: string;
  images?: string[];
  categoryId: string;
  merchantId: string;
  stock: number;
  sales: number;
  rating: number;
  reviewCount: number;
  isAvailable?: boolean;
  tags?: string[];
  soldCount?: number;
  specifications?: ProductSpec[];
  specs?: ProductSpec[];
  status: 'active' | 'inactive' | 'sold_out';
  createdAt: string;
  updatedAt: string;
}

// 商品规格
export interface ProductSpec {
  id: string;
  name: string;
  options: SpecOption[];
}

export interface SpecOption {
  id: string;
  name: string;
  price: number;
  stock: number;
}

// 商品分类
export interface Category {
  id: string;
  name: string;
  icon: string;
  parentId?: string;
  sort: number;
}

// 购物车项目
export interface CartItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  selectedSpecs?: { [specId: string]: string };
  price: number;
  totalPrice: number;
}

// 订单状态
export type OrderStatus = 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivering' | 'delivered' | 'completed' | 'cancelled';

// 订单信息
export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  merchantId: string;
  merchantName: string;
  items: OrderItem[];
  totalAmount: number;
  mealAllowanceUsed: number;
  personalPayment: number;
  status: OrderStatus;
  paymentMethod: 'meal_allowance' | 'wallet' | 'wechat' | 'alipay';
  deliveryAddress: Address;
  deliveryTime: string;
  estimatedDeliveryTime: string;
  actualDeliveryTime?: string;
  deliveryPersonName?: string;
  deliveryPersonPhone?: string;
  createdAt: string;
  updatedAt: string;
  remark?: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  quantity: number;
  price: number;
  totalPrice: number;
  selectedSpecs?: { [specId: string]: string };
}

// 餐标配置
export interface MealAllowance {
  id: string;
  enterpriseId: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'supper';
  amount: number;
  isRefundable: boolean;
  validFrom: string;
  validTo: string;
  cutoffTime: string; // 截单时间
}

// 企业信息
export interface Enterprise {
  id: string;
  name: string;
  address: string;
  contactPerson: string;
  contactPhone: string;
  contractStartDate: string;
  contractEndDate: string;
  status: 'active' | 'inactive';
  mealAllowances: MealAllowance[];
  cabinetIds: string[];
}

// 商户信息
export interface Merchant {
  id: string;
  name: string;
  description: string;
  logo: string;
  address: string;
  phone: string;
  businessHours: string;
  rating: number;
  reviewCount: number;
  deliveryFee: number;
  minOrderAmount: number;
  deliveryTime: string;
  status: 'active' | 'inactive';
  categories: string[];
  regionId: string;
}

// 智能餐柜
export interface SmartCabinet {
  id: string;
  name: string;
  location: string;
  enterpriseId: string;
  status: 'online' | 'offline' | 'maintenance';
  compartments: CabinetCompartment[];
  temperature: number;
  lastMaintenanceDate: string;
  nextMaintenanceDate: string;
}

export interface CabinetCompartment {
  id: string;
  number: string;
  size: 'small' | 'medium' | 'large';
  status: 'empty' | 'occupied' | 'reserved';
  orderId?: string;
  temperature?: number;
}

// 配送员信息
export interface DeliveryPerson {
  id: string;
  name: string;
  phone: string;
  vehicleType: string;
  vehicleNumber: string;
  status: 'available' | 'busy' | 'offline';
  currentLocation?: {
    lat: number;
    lng: number;
  };
  regionId: string;
  rating: number;
  completedOrders: number;
}

// 配送任务
export interface DeliveryTask {
  id: string;
  orderId: string;
  deliveryPersonId: string;
  merchantId: string;
  pickupAddress: string;
  deliveryAddress: string;
  estimatedPickupTime: string;
  estimatedDeliveryTime: string;
  actualPickupTime?: string;
  actualDeliveryTime?: string;
  status: 'assigned' | 'pickup' | 'delivering' | 'delivered' | 'failed';
  route?: {
    lat: number;
    lng: number;
  }[];
}

// API 响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 评价信息
export interface Review {
  id: string;
  orderId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  productId: string;
  rating: number;
  content: string;
  images?: string[];
  merchantReply?: string;
  createdAt: string;
}

// 钱包信息
export interface Wallet {
  id: string;
  userId: string;
  balance: number;
  mealAllowanceBalance: {
    breakfast: number;
    lunch: number;
    dinner: number;
    supper: number;
  };
  transactions: WalletTransaction[];
}

export interface WalletTransaction {
  id: string;
  type: 'recharge' | 'payment' | 'refund' | 'meal_allowance';
  amount: number;
  description: string;
  orderId?: string;
  createdAt: string;
}

// 通知信息
export interface Notification {
  id: string;
  userId: string;
  title: string;
  content: string;
  type: 'order' | 'system' | 'promotion';
  isRead: boolean;
  createdAt: string;
  data?: any;
}

// 地址信息
export interface Address {
  id: string;
  userId: string;
  name: string;
  phone: string;
  address: string;
  isDefault: boolean;
  lat?: number;
  lng?: number;
}

// 优惠券信息
export interface Coupon {
  id: string;
  name: string;
  description: string;
  type: 'discount' | 'amount' | 'free_delivery';
  value: number;
  minOrderAmount: number;
  validFrom: string;
  validTo: string;
  usageLimit: number;
  usedCount: number;
  status: 'active' | 'inactive';
  applicableProducts?: string[];
  applicableMerchants?: string[];
}

// 用户优惠券
export interface UserCoupon {
  id: string;
  userId: string;
  couponId: string;
  coupon: Coupon;
  status: 'unused' | 'used' | 'expired';
  usedAt?: string;
  orderId?: string;
  receivedAt: string;
}