import { createBrowserRouter, Navigate, RouterProvider } from 'react-router-dom';
import { useAppStore } from '../store';

// Layouts
import EmployeeLayout from '../layouts/EmployeeLayout';
import MerchantLayout from '../layouts/MerchantLayout';
import EnterpriseLayout from '../layouts/EnterpriseLayout';
import PlatformLayout from '../layouts/PlatformLayout';

// 员工端页面
import EmployeeHome from '../pages/employee/Home';
import ProductDetail from '../pages/employee/ProductDetail';
import Cart from '../pages/employee/Cart';
import Orders from '../pages/employee/Orders';
import Profile from '../pages/employee/Profile';
import Login from '../pages/auth/Login';
import RoleSelect from '../pages/auth/RoleSelect';

// 商户端页面
import MerchantDashboard from '../pages/merchant/Dashboard';
import MerchantProducts from '../pages/merchant/Products';
import MerchantOrders from '../pages/merchant/Orders';
import MerchantSettings from '../pages/merchant/Settings';

// 企业端页面
import EnterpriseDashboard from '../pages/enterprise/Dashboard';
import Employees from '../pages/enterprise/Employees';
import MealAllowance from '../pages/enterprise/MealAllowance';
import Statistics from '../pages/enterprise/Statistics';
import Attendance from '../pages/enterprise/Attendance';
import EnterpriseSettings from '../pages/enterprise/Settings';

// 平台端页面
import PlatformDashboard from '../pages/platform/Dashboard';
import MerchantManagement from '../pages/platform/MerchantManagement';
import EnterpriseManagement from '../pages/platform/EnterpriseManagement';
import FinancialManagement from '../pages/platform/FinancialManagement';
import CustomerService from '../pages/platform/CustomerService';
import DeliveryManagement from '../pages/platform/DeliveryManagement';
import CentralKitchen from '../pages/platform/CentralKitchen';
import RegionManagement from '../pages/platform/RegionManagement';
import SystemSettings from '../pages/platform/SystemSettings';

// 路由守卫组件
const ProtectedRoute = ({ children, requiredRole }: { children: React.ReactNode; requiredRole?: string }) => {
  const { currentRole } = useAppStore();
  
  if (requiredRole && currentRole !== requiredRole) {
    return <Navigate to="/role-select" replace />;
  }
  
  return <>{children}</>;
};

const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/role-select" replace />
  },
  {
    path: '/login',
    element: <Login />
  },
  {
    path: '/role-select',
    element: <RoleSelect />
  },
  // 员工端路由
  {
    path: '/employee',
    element: (
      <ProtectedRoute requiredRole="employee">
        <EmployeeLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <EmployeeHome />
      },
      {
        path: 'product/:id',
        element: <ProductDetail />
      },
      {
        path: 'cart',
        element: <Cart />
      },
      {
        path: 'orders',
        element: <Orders />
      },
      {
        path: 'profile',
        element: <Profile />
      }
    ]
  },
  // 商户端路由
  {
    path: '/merchant',
    element: (
      <ProtectedRoute requiredRole="merchant">
        <MerchantLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/merchant/dashboard" replace />
      },
      {
        path: 'dashboard',
        element: <MerchantDashboard />
      },
      {
        path: 'products',
        element: <MerchantProducts />
      },
      {
        path: 'orders',
        element: <MerchantOrders />
      },
      {
        path: 'settings',
        element: <MerchantSettings />
      }
    ]
  },
  // 企业端路由
  {
    path: '/enterprise',
    element: (
      <ProtectedRoute requiredRole="enterprise_admin">
        <EnterpriseLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/enterprise/dashboard" replace />
      },
      {
        path: 'dashboard',
        element: <EnterpriseDashboard />
      },
      {
        path: 'employees',
        element: <Employees />
      },
      {
        path: 'meal-allowance',
        element: <MealAllowance />
      },
      {
        path: 'statistics',
        element: <Statistics />
      },
      {
        path: 'attendance',
        element: <Attendance />
      },
      {
        path: 'settings',
        element: <EnterpriseSettings />
      }
    ]
  },
  // 平台端路由
  {
    path: '/platform',
    element: (
      <ProtectedRoute requiredRole="platform_admin">
        <PlatformLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/platform/dashboard" replace />
      },
      {
        path: 'dashboard',
        element: <PlatformDashboard />
      },
      {
        path: 'merchants',
        element: <MerchantManagement />
      },
      {
        path: 'enterprises',
        element: <EnterpriseManagement />
      },
      {
        path: 'finance',
        element: <FinancialManagement />
      },
      {
        path: 'customer-service',
        element: <CustomerService />
      },
      {
        path: 'delivery',
        element: <DeliveryManagement />
      },
      {
        path: 'kitchen',
        element: <CentralKitchen />
      },
      {
        path: 'regions',
        element: <RegionManagement />
      },
      {
        path: 'settings',
        element: <SystemSettings />
      }
    ]
  },
  {
    path: '*',
    element: <Navigate to="/role-select" replace />
  }
]);

const AppRouter = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;