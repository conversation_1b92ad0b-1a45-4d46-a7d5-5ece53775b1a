import { useState } from 'react';
import { Search, Filter, Eye, Clock, Package, Truck, CheckCircle, XCircle, Phone, MessageCircle, FileText } from 'lucide-react';
import { Order, OrderStatus } from '../../types';
import { toast } from 'sonner';

const Orders = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | OrderStatus>('all');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetail, setShowOrderDetail] = useState(false);

  // 模拟订单数据
  const [orders, setOrders] = useState<Order[]>([
    {
      id: '1',
      orderNumber: 'DD202412150001',
      userId: 'user1',
      merchantId: 'merchant1',
      merchantName: '美味餐厅',
      items: [
        {
          id: '1',
          productId: '1',
          productName: '宫保鸡丁套餐',
          productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=kung_pao_chicken_set_meal&image_size=square',
          price: 28.00,
          quantity: 1,
          totalPrice: 28.00
        }
      ],
      totalAmount: 28.00,
      mealAllowanceUsed: 28.00,
      personalPayment: 0,
      status: 'pending',
      paymentMethod: 'meal_allowance',
      deliveryAddress: {
        id: '1',
        userId: 'user1',
        name: '张三',
        phone: '138****1234',
        address: '科技园A座12楼',
        isDefault: true
      },
      deliveryTime: '12:30',
      estimatedDeliveryTime: '12:30',
      remark: '不要辣',
      createdAt: '2024-12-15T10:30:00Z',
      updatedAt: '2024-12-15T10:30:00Z'
    },
    {
      id: '2',
      orderNumber: 'DD202412150002',
      userId: 'user2',
      merchantId: 'merchant1',
      merchantName: '美味餐厅',
      items: [
        {
          id: '2',
          productId: '2',
          productName: '红烧肉套餐',
          productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=braised_pork_belly_set_meal&image_size=square',
          price: 32.00,
          quantity: 2,
          totalPrice: 64.00
        }
      ],
      totalAmount: 64.00,
      mealAllowanceUsed: 50.00,
      personalPayment: 14.00,
      status: 'preparing',
      paymentMethod: 'wechat',
      deliveryAddress: {
        id: '2',
        userId: 'user2',
        name: '李四',
        phone: '139****5678',
        address: '科技园B座8楼',
        isDefault: true
      },
      deliveryTime: '12:45',
      estimatedDeliveryTime: '12:45',
      createdAt: '2024-12-15T10:15:00Z',
      updatedAt: '2024-12-15T10:45:00Z'
    },
    {
      id: '3',
      orderNumber: 'DD202412150003',
      userId: 'user3',
      merchantId: 'merchant1',
      merchantName: '美味餐厅',
      items: [
        {
          id: '3',
          productId: '3',
          productName: '麻婆豆腐套餐',
          productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=mapo_tofu_set_meal&image_size=square',
          price: 25.00,
          quantity: 1,
          totalPrice: 25.00
        }
      ],
      totalAmount: 25.00,
      mealAllowanceUsed: 25.00,
      personalPayment: 0,
      status: 'completed',
      paymentMethod: 'alipay',
      deliveryAddress: {
        id: '3',
        userId: 'user3',
        name: '王五',
        phone: '137****9012',
        address: '科技园C座15楼',
        isDefault: true
      },
      deliveryTime: '11:30',
      estimatedDeliveryTime: '11:30',
      createdAt: '2024-12-15T09:30:00Z',
      updatedAt: '2024-12-15T11:45:00Z'
    }
  ]);

  const statusOptions = [
    { value: 'all', label: '全部订单', count: orders.length },
    { value: 'pending', label: '待接单', count: orders.filter(o => o.status === 'pending').length },
    { value: 'preparing', label: '制作中', count: orders.filter(o => o.status === 'preparing').length },
    { value: 'delivering', label: '配送中', count: orders.filter(o => o.status === 'delivering').length },
    { value: 'completed', label: '已完成', count: orders.filter(o => o.status === 'completed').length },
    { value: 'cancelled', label: '已取消', count: orders.filter(o => o.status === 'cancelled').length }
  ];

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.deliveryAddress.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'preparing': return <Package className="h-4 w-4" />;
      case 'delivering': return <Truck className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'preparing': return 'bg-blue-100 text-blue-800';
      case 'delivering': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: OrderStatus) => {
    switch (status) {
      case 'pending': return '待接单';
      case 'preparing': return '制作中';
      case 'delivering': return '配送中';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  const handleStatusUpdate = (orderId: string, newStatus: OrderStatus) => {
    setOrders(prev => prev.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus, updatedAt: new Date().toISOString() }
        : order
    ));
    toast.success('订单状态已更新');
  };

  const getNextStatus = (currentStatus: OrderStatus): OrderStatus | null => {
    switch (currentStatus) {
      case 'pending': return 'preparing';
      case 'preparing': return 'delivering';
      case 'delivering': return 'completed';
      default: return null;
    }
  };

  const getNextStatusText = (currentStatus: OrderStatus): string => {
    switch (currentStatus) {
      case 'pending': return '接单';
      case 'preparing': return '开始配送';
      case 'delivering': return '完成订单';
      default: return '';
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  };

  const OrderDetailModal = ({ order, onClose }: { order: Order; onClose: () => void }) => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold">订单详情</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="space-y-6">
            {/* 订单基本信息 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">订单号</p>
                  <p className="font-medium">{order.orderNumber}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">订单状态</p>
                  <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                    getStatusColor(order.status)
                  }`}>
                    {getStatusIcon(order.status)}
                    <span>{getStatusText(order.status)}</span>
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-600">下单时间</p>
                  <p className="font-medium">{formatTime(order.createdAt)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">期望送达</p>
                  <p className="font-medium">{order.deliveryTime}</p>
                </div>
              </div>
            </div>

            {/* 客户信息 */}
            <div>
              <h4 className="font-medium mb-3">客户信息</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">客户姓名</p>
                    <p className="font-medium">{order.deliveryAddress.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">联系电话</p>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{order.deliveryAddress.phone}</span>
                      <button className="text-blue-600 hover:text-blue-700">
                        <Phone className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div className="col-span-2">
                    <p className="text-sm text-gray-600">配送地址</p>
                    <p className="font-medium">{order.deliveryAddress.address}</p>
                  </div>
                  {order.remark && (
                    <div className="col-span-2">
                      <p className="text-sm text-gray-600">备注</p>
                      <p className="font-medium text-orange-600">{order.remark}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 商品信息 */}
            <div>
              <h4 className="font-medium mb-3">商品明细</h4>
              <div className="space-y-3">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-center space-x-3 bg-gray-50 rounded-lg p-3">
                    <img
                      src={item.productImage}
                      alt={item.productName}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <p className="font-medium">{item.productName}</p>
                      <p className="text-sm text-gray-600">¥{item.price.toFixed(2)} × {item.quantity}</p>
                    </div>
                    <p className="font-semibold">¥{item.totalPrice.toFixed(2)}</p>
                  </div>
                ))}
              </div>
              <div className="border-t pt-3 mt-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium">订单总额</span>
                  <span className="text-lg font-bold text-orange-600">¥{order.totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            {order.status !== 'completed' && order.status !== 'cancelled' && (
              <div className="flex space-x-3">
                {order.status === 'pending' && (
                  <>
                    <button
                      onClick={() => {
                        handleStatusUpdate(order.id, 'cancelled');
                        onClose();
                      }}
                      className="flex-1 px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50"
                    >
                      拒绝订单
                    </button>
                    <button
                      onClick={() => {
                        handleStatusUpdate(order.id, 'preparing');
                        onClose();
                      }}
                      className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                    >
                      接受订单
                    </button>
                  </>
                )}
                
                {getNextStatus(order.status) && (
                  <button
                    onClick={() => {
                      const nextStatus = getNextStatus(order.status);
                      if (nextStatus) {
                        handleStatusUpdate(order.id, nextStatus);
                        onClose();
                      }
                    }}
                    className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                  >
                    {getNextStatusText(order.status)}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
      </div>

      {/* 状态统计 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {statusOptions.map((option) => (
          <button
            key={option.value}
            onClick={() => setStatusFilter(option.value as any)}
            className={`p-4 rounded-lg border text-center transition-colors ${
              statusFilter === option.value
                ? 'bg-orange-50 border-orange-200 text-orange-700'
                : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
            }`}
          >
            <p className="text-2xl font-bold">{option.count}</p>
            <p className="text-sm">{option.label}</p>
          </button>
        ))}
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索订单号或客户姓名..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>
        </div>
      </div>

      {/* 订单列表 */}
      <div className="space-y-4">
        {filteredOrders.map((order) => (
          <div key={order.id} className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div>
                  <p className="font-semibold text-gray-900">#{order.orderNumber}</p>
                  <p className="text-sm text-gray-500">{formatTime(order.createdAt)}</p>
                </div>
                <span className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium ${
                  getStatusColor(order.status)
                }`}>
                  {getStatusIcon(order.status)}
                  <span>{getStatusText(order.status)}</span>
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => {
                    setSelectedOrder(order);
                    setShowOrderDetail(true);
                  }}
                  className="flex items-center space-x-1 px-3 py-1 text-blue-600 hover:text-blue-700"
                >
                  <Eye className="h-4 w-4" />
                  <span>查看详情</span>
                </button>
                
                {getNextStatus(order.status) && (
                  <button
                    onClick={() => {
                      const nextStatus = getNextStatus(order.status);
                      if (nextStatus) {
                        handleStatusUpdate(order.id, nextStatus);
                      }
                    }}
                    className="px-3 py-1 bg-orange-600 text-white rounded hover:bg-orange-700"
                  >
                    {getNextStatusText(order.status)}
                  </button>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600">客户信息</p>
                <p className="font-medium">{order.deliveryAddress.name}</p>
                <p className="text-sm text-gray-500">{order.deliveryAddress.phone}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">配送地址</p>
                <p className="font-medium">{order.deliveryAddress.address}</p>
                <p className="text-sm text-gray-500">期望送达: {order.deliveryTime}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">订单金额</p>
                <p className="text-lg font-bold text-orange-600">¥{order.totalAmount.toFixed(2)}</p>
                <p className="text-sm text-gray-500">{order.items.length} 个商品</p>
              </div>
            </div>
            
            {order.remark && (
              <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                <p className="text-sm text-gray-600">备注</p>
                <p className="font-medium text-yellow-800">{order.remark}</p>
              </div>
            )}
          </div>
        ))}
        
        {filteredOrders.length === 0 && (
          <div className="text-center py-12 bg-white rounded-lg border">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">暂无订单</p>
          </div>
        )}
      </div>

      {/* 订单详情模态框 */}
      {showOrderDetail && selectedOrder && (
        <OrderDetailModal 
          order={selectedOrder} 
          onClose={() => {
            setShowOrderDetail(false);
            setSelectedOrder(null);
          }} 
        />
      )}
    </div>
  );
};

export default Orders;