import { useState } from 'react';
import { 
  Search, 
  Filter, 
  MapPin, 
  Truck, 
  Clock, 
  User, 
  Phone, 
  Star, 
  TrendingUp, 
  Package, 
  Navigation, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  MoreHorizontal,
  Users,
  Activity,
  DollarSign,
  Route
} from 'lucide-react';
import { toast } from 'sonner';

interface DeliveryPerson {
  id: string;
  name: string;
  phone: string;
  email: string;
  avatar?: string;
  status: 'online' | 'offline' | 'busy' | 'break';
  rating: number;
  totalOrders: number;
  completedOrders: number;
  currentLocation: {
    lat: number;
    lng: number;
    address: string;
  };
  vehicle: {
    type: 'bicycle' | 'motorcycle' | 'car' | 'walking';
    plateNumber?: string;
  };
  workingHours: {
    start: string;
    end: string;
  };
  earnings: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  joinDate: string;
  lastActive: string;
}

interface DeliveryOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  merchantName: string;
  pickupAddress: string;
  deliveryAddress: string;
  distance: number; // km
  estimatedTime: number; // minutes
  actualTime?: number; // minutes
  status: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled';
  assignedTo?: string;
  deliveryFee: number;
  orderValue: number;
  createdAt: string;
  assignedAt?: string;
  pickedUpAt?: string;
  deliveredAt?: string;
  priority: 'normal' | 'high' | 'urgent';
}

interface DeliveryStats {
  totalDeliveryPersons: number;
  onlineDeliveryPersons: number;
  activeOrders: number;
  completedToday: number;
  avgDeliveryTime: number;
  customerSatisfaction: number;
  totalRevenue: number;
  avgOrderValue: number;
}

const DeliveryManagement = () => {
  const [activeTab, setActiveTab] = useState<'personnel' | 'orders' | 'analytics'>('personnel');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'online' | 'offline' | 'busy' | 'break'>('all');
  const [orderStatusFilter, setOrderStatusFilter] = useState<'all' | 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled'>('all');
  const [selectedPerson, setSelectedPerson] = useState<DeliveryPerson | null>(null);
  const [showPersonDetail, setShowPersonDetail] = useState(false);

  // 模拟配送统计数据
  const deliveryStats: DeliveryStats = {
    totalDeliveryPersons: 156,
    onlineDeliveryPersons: 89,
    activeOrders: 234,
    completedToday: 1247,
    avgDeliveryTime: 28, // minutes
    customerSatisfaction: 4.7,
    totalRevenue: 45680,
    avgOrderValue: 68.5
  };

  // 模拟配送员数据
  const deliveryPersons: DeliveryPerson[] = [
    {
      id: '1',
      name: '张师傅',
      phone: '13800138001',
      email: '<EMAIL>',
      status: 'online',
      rating: 4.8,
      totalOrders: 1247,
      completedOrders: 1198,
      currentLocation: {
        lat: 39.9042,
        lng: 116.4074,
        address: '北京市朝阳区建国门外大街'
      },
      vehicle: {
        type: 'motorcycle',
        plateNumber: '京A12345'
      },
      workingHours: {
        start: '08:00',
        end: '20:00'
      },
      earnings: {
        today: 280,
        thisWeek: 1680,
        thisMonth: 6720
      },
      joinDate: '2023-06-15',
      lastActive: '2024-01-15 14:30'
    },
    {
      id: '2',
      name: '李小明',
      phone: '13800138002',
      email: '<EMAIL>',
      status: 'busy',
      rating: 4.6,
      totalOrders: 892,
      completedOrders: 856,
      currentLocation: {
        lat: 39.9042,
        lng: 116.4074,
        address: '北京市海淀区中关村大街'
      },
      vehicle: {
        type: 'bicycle'
      },
      workingHours: {
        start: '09:00',
        end: '18:00'
      },
      earnings: {
        today: 195,
        thisWeek: 1365,
        thisMonth: 5460
      },
      joinDate: '2023-08-20',
      lastActive: '2024-01-15 14:25'
    },
    {
      id: '3',
      name: '王大力',
      phone: '13800138003',
      email: '<EMAIL>',
      status: 'offline',
      rating: 4.9,
      totalOrders: 2156,
      completedOrders: 2089,
      currentLocation: {
        lat: 39.9042,
        lng: 116.4074,
        address: '北京市西城区西单北大街'
      },
      vehicle: {
        type: 'car',
        plateNumber: '京B67890'
      },
      workingHours: {
        start: '07:00',
        end: '19:00'
      },
      earnings: {
        today: 0,
        thisWeek: 2100,
        thisMonth: 8400
      },
      joinDate: '2023-03-10',
      lastActive: '2024-01-14 19:15'
    }
  ];

  // 模拟配送订单数据
  const deliveryOrders: DeliveryOrder[] = [
    {
      id: '1',
      orderNumber: 'ORD-2024-001247',
      customerName: '张三',
      customerPhone: '13900139001',
      merchantName: '川味小厨',
      pickupAddress: '北京市朝阳区建国门外大街1号',
      deliveryAddress: '北京市朝阳区国贸中心2号楼',
      distance: 2.5,
      estimatedTime: 25,
      status: 'assigned',
      assignedTo: '张师傅',
      deliveryFee: 8,
      orderValue: 89,
      priority: 'normal',
      createdAt: '2024-01-15 14:20',
      assignedAt: '2024-01-15 14:22'
    },
    {
      id: '2',
      orderNumber: 'ORD-2024-001246',
      customerName: '李四',
      customerPhone: '13900139002',
      merchantName: '粤菜馆',
      pickupAddress: '北京市海淀区中关村大街15号',
      deliveryAddress: '北京市海淀区清华大学东门',
      distance: 1.8,
      estimatedTime: 20,
      actualTime: 18,
      status: 'delivered',
      assignedTo: '李小明',
      deliveryFee: 6,
      orderValue: 156,
      priority: 'high',
      createdAt: '2024-01-15 13:45',
      assignedAt: '2024-01-15 13:47',
      pickedUpAt: '2024-01-15 13:55',
      deliveredAt: '2024-01-15 14:13'
    },
    {
      id: '3',
      orderNumber: 'ORD-2024-001245',
      customerName: '王五',
      customerPhone: '13900139003',
      merchantName: '湘菜坊',
      pickupAddress: '北京市西城区西单北大街8号',
      deliveryAddress: '北京市西城区金融街购物中心',
      distance: 3.2,
      estimatedTime: 30,
      status: 'pending',
      deliveryFee: 10,
      orderValue: 234,
      priority: 'urgent',
      createdAt: '2024-01-15 14:30'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-800';
      case 'offline':
        return 'bg-gray-100 text-gray-800';
      case 'busy':
        return 'bg-yellow-100 text-yellow-800';
      case 'break':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'busy':
        return '忙碌';
      case 'break':
        return '休息';
      default:
        return '未知';
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'assigned':
        return 'bg-blue-100 text-blue-800';
      case 'picked_up':
        return 'bg-purple-100 text-purple-800';
      case 'in_transit':
        return 'bg-indigo-100 text-indigo-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待分配';
      case 'assigned':
        return '已分配';
      case 'picked_up':
        return '已取餐';
      case 'in_transit':
        return '配送中';
      case 'delivered':
        return '已送达';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'normal':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return '紧急';
      case 'high':
        return '高';
      case 'normal':
        return '普通';
      default:
        return '未知';
    }
  };

  const getVehicleIcon = (type: string) => {
    switch (type) {
      case 'bicycle':
        return '🚲';
      case 'motorcycle':
        return '🏍️';
      case 'car':
        return '🚗';
      case 'walking':
        return '🚶';
      default:
        return '🚲';
    }
  };

  const getVehicleText = (type: string) => {
    switch (type) {
      case 'bicycle':
        return '自行车';
      case 'motorcycle':
        return '摩托车';
      case 'car':
        return '汽车';
      case 'walking':
        return '步行';
      default:
        return '未知';
    }
  };

  const filteredPersons = deliveryPersons.filter(person => {
    const matchesSearch = person.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         person.phone.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || person.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredOrders = deliveryOrders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.merchantName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = orderStatusFilter === 'all' || order.status === orderStatusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleViewPerson = (person: DeliveryPerson) => {
    setSelectedPerson(person);
    setShowPersonDetail(true);
  };

  const handleAssignOrder = (orderId: string, personId: string) => {
    toast.success('订单分配成功');
  };

  const handleUpdatePersonStatus = (personId: string, status: string) => {
    toast.success(`配送员状态已更新为${getStatusText(status)}`);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">配送管理</h1>
          <p className="text-gray-600 mt-1">管理配送人员和配送订单</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <User className="h-4 w-4 mr-2" />
            添加配送员
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <Route className="h-4 w-4 mr-2" />
            路线优化
          </button>
        </div>
      </div>

      {/* 配送统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">配送员总数</p>
              <p className="text-2xl font-bold text-gray-900">{deliveryStats.totalDeliveryPersons}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+8.2%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">在线配送员</p>
              <p className="text-2xl font-bold text-gray-900">{deliveryStats.onlineDeliveryPersons}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Activity className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">在线率: {Math.round((deliveryStats.onlineDeliveryPersons / deliveryStats.totalDeliveryPersons) * 100)}%</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">活跃订单</p>
              <p className="text-2xl font-bold text-gray-900">{deliveryStats.activeOrders}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Package className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <Clock className="h-4 w-4 text-yellow-500" />
            <span className="text-sm text-gray-500 ml-1">待配送中</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">今日完成</p>
              <p className="text-2xl font-bold text-gray-900">{deliveryStats.completedToday}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+15.3%</span>
            <span className="text-sm text-gray-500 ml-2">vs 昨日</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均配送时间</p>
              <p className="text-2xl font-bold text-gray-900">{deliveryStats.avgDeliveryTime}分钟</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">目标: &lt;30分钟</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">客户满意度</p>
              <p className="text-2xl font-bold text-gray-900">{deliveryStats.customerSatisfaction}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Star className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <Star className="h-4 w-4 text-yellow-500 fill-current" />
            <span className="text-sm text-gray-500 ml-1">5分制评分</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">配送收入</p>
              <p className="text-2xl font-bold text-gray-900">¥{deliveryStats.totalRevenue.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+22.1%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均订单价值</p>
              <p className="text-2xl font-bold text-gray-900">¥{deliveryStats.avgOrderValue}</p>
            </div>
            <div className="p-3 bg-indigo-100 rounded-lg">
              <Package className="h-6 w-6 text-indigo-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">包含配送费</span>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('personnel')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'personnel'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              配送人员
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'orders'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              配送订单
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'analytics'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              数据分析
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* 搜索和筛选 */}
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder={activeTab === 'personnel' ? '搜索配送员姓名或手机号...' : '搜索订单号、客户或商户...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              {activeTab === 'personnel' ? (
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">全部状态</option>
                  <option value="online">在线</option>
                  <option value="offline">离线</option>
                  <option value="busy">忙碌</option>
                  <option value="break">休息</option>
                </select>
              ) : (
                <select
                  value={orderStatusFilter}
                  onChange={(e) => setOrderStatusFilter(e.target.value as any)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">全部状态</option>
                  <option value="pending">待分配</option>
                  <option value="assigned">已分配</option>
                  <option value="picked_up">已取餐</option>
                  <option value="in_transit">配送中</option>
                  <option value="delivered">已送达</option>
                  <option value="cancelled">已取消</option>
                </select>
              )}
            </div>
          </div>

          {/* 配送人员列表 */}
          {activeTab === 'personnel' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      配送员信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      评分/订单
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      交通工具
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      今日收入
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      位置
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPersons.map((person) => (
                    <tr key={person.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <User className="h-5 w-5 text-gray-500" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{person.name}</div>
                            <div className="text-sm text-gray-500">{person.phone}</div>
                            <div className="text-xs text-gray-500">入职: {person.joinDate}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(person.status)}`}>
                          {getStatusText(person.status)}
                        </span>
                        <div className="text-xs text-gray-500 mt-1">
                          最后活跃: {person.lastActive}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-sm font-medium text-gray-900 ml-1">{person.rating}</span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {person.completedOrders}/{person.totalOrders} 完成
                        </div>
                        <div className="text-xs text-gray-500">
                          完成率: {Math.round((person.completedOrders / person.totalOrders) * 100)}%
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-lg mr-2">{getVehicleIcon(person.vehicle.type)}</span>
                          <div>
                            <div className="text-sm font-medium text-gray-900">{getVehicleText(person.vehicle.type)}</div>
                            {person.vehicle.plateNumber && (
                              <div className="text-xs text-gray-500">{person.vehicle.plateNumber}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">¥{person.earnings.today}</div>
                        <div className="text-xs text-gray-500">本周: ¥{person.earnings.thisWeek}</div>
                        <div className="text-xs text-gray-500">本月: ¥{person.earnings.thisMonth}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                          <div className="text-sm text-gray-900 max-w-xs truncate">
                            {person.currentLocation.address}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewPerson(person)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <User className="h-4 w-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            <Phone className="h-4 w-4" />
                          </button>
                          <button className="text-purple-600 hover:text-purple-900">
                            <MapPin className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            <MoreHorizontal className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 配送订单列表 */}
          {activeTab === 'orders' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      订单信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      客户信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      配送信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态/优先级
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      配送员
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      费用
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredOrders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{order.orderNumber}</div>
                          <div className="text-sm text-gray-600">{order.merchantName}</div>
                          <div className="text-xs text-gray-500">{order.createdAt}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{order.customerName}</div>
                          <div className="text-sm text-gray-500">{order.customerPhone}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <div className="text-xs text-gray-500">
                            取餐: {order.pickupAddress}
                          </div>
                          <div className="text-xs text-gray-500">
                            送达: {order.deliveryAddress}
                          </div>
                          <div className="text-xs text-gray-500">
                            距离: {order.distance}km | 预计: {order.estimatedTime}分钟
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getOrderStatusColor(order.status)}`}>
                            {getOrderStatusText(order.status)}
                          </span>
                          <br />
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(order.priority)}`}>
                            {getPriorityText(order.priority)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {order.assignedTo || '未分配'}
                        </div>
                        {order.assignedAt && (
                          <div className="text-xs text-gray-500">
                            分配时间: {order.assignedAt}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">¥{order.deliveryFee}</div>
                        <div className="text-xs text-gray-500">订单: ¥{order.orderValue}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            <Navigation className="h-4 w-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            <Phone className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            <MoreHorizontal className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 数据分析 */}
          {activeTab === 'analytics' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">配送数据分析</h3>
                <p className="text-gray-500">配送效率、路线优化、成本分析等数据图表</p>
                <p className="text-sm text-gray-400 mt-2">功能开发中...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 配送员详情弹窗 */}
      {showPersonDetail && selectedPerson && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowPersonDetail(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">配送员详情</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedPerson.status)}`}>
                    {getStatusText(selectedPerson.status)}
                  </span>
                </div>
              </div>
              
              <div className="bg-white px-6 py-4 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">基本信息</h4>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <User className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{selectedPerson.name}</div>
                          <div className="text-xs text-gray-500">配送员</div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-600">{selectedPerson.phone}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-lg mr-3">{getVehicleIcon(selectedPerson.vehicle.type)}</span>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{getVehicleText(selectedPerson.vehicle.type)}</div>
                          {selectedPerson.vehicle.plateNumber && (
                            <div className="text-xs text-gray-500">{selectedPerson.vehicle.plateNumber}</div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-600">
                          工作时间: {selectedPerson.workingHours.start} - {selectedPerson.workingHours.end}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-lg font-semibold text-gray-900 mb-4">绩效数据</h5>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">评分：</span>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                          <span className="text-sm font-medium">{selectedPerson.rating}</span>
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">总订单：</span>
                        <span className="text-sm font-medium">{selectedPerson.totalOrders}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">完成订单：</span>
                        <span className="text-sm font-medium">{selectedPerson.completedOrders}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">完成率：</span>
                        <span className="text-sm font-medium">
                          {Math.round((selectedPerson.completedOrders / selectedPerson.totalOrders) * 100)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">入职时间：</span>
                        <span className="text-sm font-medium">{selectedPerson.joinDate}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="text-lg font-semibold text-gray-900 mb-4">收入统计</h5>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">¥{selectedPerson.earnings.today}</div>
                      <div className="text-sm text-gray-600">今日收入</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">¥{selectedPerson.earnings.thisWeek}</div>
                      <div className="text-sm text-gray-600">本周收入</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">¥{selectedPerson.earnings.thisMonth}</div>
                      <div className="text-sm text-gray-600">本月收入</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="text-lg font-semibold text-gray-900 mb-4">当前位置</h5>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <MapPin className="h-5 w-5 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-600">{selectedPerson.currentLocation.address}</span>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      坐标: {selectedPerson.currentLocation.lat}, {selectedPerson.currentLocation.lng}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                <button
                  onClick={() => setShowPersonDetail(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  关闭
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                  编辑信息
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeliveryManagement;