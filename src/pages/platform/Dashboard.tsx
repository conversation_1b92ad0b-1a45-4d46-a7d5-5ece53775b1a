import { useState } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Building2, 
  Users, 
  CreditCard, 
  ShoppingCart,
  DollarSign,
  Package,
  Truck,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  PieChart
} from 'lucide-react';

interface StatCard {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down';
  icon: React.ComponentType<any>;
  color: string;
}

interface RecentActivity {
  id: string;
  type: 'merchant' | 'enterprise' | 'order' | 'finance';
  title: string;
  description: string;
  time: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

const Dashboard = () => {
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month'>('week');

  const stats: StatCard[] = [
    {
      title: '总商户数',
      value: '1,247',
      change: '+8.2%',
      trend: 'up',
      icon: Building2,
      color: 'blue'
    },
    {
      title: '企业客户',
      value: '356',
      change: '+12.5%',
      trend: 'up',
      icon: Users,
      color: 'green'
    },
    {
      title: '平台收入',
      value: '¥2,847,392',
      change: '+15.3%',
      trend: 'up',
      icon: DollarSign,
      color: 'yellow'
    },
    {
      title: '总订单量',
      value: '45,678',
      change: '+6.8%',
      trend: 'up',
      icon: ShoppingCart,
      color: 'purple'
    },
    {
      title: '活跃配送员',
      value: '892',
      change: '-2.1%',
      trend: 'down',
      icon: Truck,
      color: 'red'
    },
    {
      title: '商品总数',
      value: '12,456',
      change: '+4.7%',
      trend: 'up',
      icon: Package,
      color: 'indigo'
    }
  ];

  const recentActivities: RecentActivity[] = [
    {
      id: '1',
      type: 'merchant',
      title: '新商户申请',
      description: '"川味小厨"提交入驻申请，等待审核',
      time: '2分钟前',
      status: 'info'
    },
    {
      id: '2',
      type: 'enterprise',
      title: '企业合同续签',
      description: '科技有限公司合同即将到期，需要续签',
      time: '15分钟前',
      status: 'warning'
    },
    {
      id: '3',
      type: 'finance',
      title: '财务对账完成',
      description: '本周财务对账已完成，无异常',
      time: '1小时前',
      status: 'success'
    },
    {
      id: '4',
      type: 'order',
      title: '订单异常处理',
      description: '订单 #12345 配送超时，已联系客服处理',
      time: '2小时前',
      status: 'error'
    },
    {
      id: '5',
      type: 'merchant',
      title: '商户认证通过',
      description: '"美味餐厅"认证审核通过，已开通服务',
      time: '3小时前',
      status: 'success'
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-500 text-blue-600 bg-blue-50',
      green: 'bg-green-500 text-green-600 bg-green-50',
      yellow: 'bg-yellow-500 text-yellow-600 bg-yellow-50',
      purple: 'bg-purple-500 text-purple-600 bg-purple-50',
      red: 'bg-red-500 text-red-600 bg-red-50',
      indigo: 'bg-indigo-500 text-indigo-600 bg-indigo-50'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和时间范围选择 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据概览</h1>
          <p className="text-gray-600 mt-1">平台运营数据总览</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <div className="flex rounded-lg bg-gray-100 p-1">
            {(['today', 'week', 'month'] as const).map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`
                  px-4 py-2 text-sm font-medium rounded-md transition-colors
                  ${timeRange === range 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                  }
                `}
              >
                {range === 'today' ? '今日' : range === 'week' ? '本周' : '本月'}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown;
          const colorClasses = getColorClasses(stat.color).split(' ');
          
          return (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${colorClasses[2]}`}>
                  <Icon className={`h-6 w-6 ${colorClasses[1]}`} />
                </div>
              </div>
              <div className="flex items-center mt-4">
                <TrendIcon className={`h-4 w-4 ${
                  stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                }`} />
                <span className={`text-sm font-medium ml-1 ${
                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500 ml-2">vs 上周</span>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 最近活动 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">最近活动</h3>
              <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                查看全部
              </button>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {getStatusIcon(activity.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                    <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">快速操作</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-2 gap-4">
              <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <Building2 className="h-8 w-8 text-blue-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">商户审核</span>
                <span className="text-xs text-gray-500 mt-1">3个待审核</span>
              </button>
              <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <Users className="h-8 w-8 text-green-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">企业管理</span>
                <span className="text-xs text-gray-500 mt-1">1个待续签</span>
              </button>
              <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <CreditCard className="h-8 w-8 text-yellow-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">财务对账</span>
                <span className="text-xs text-gray-500 mt-1">本周已完成</span>
              </button>
              <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <BarChart3 className="h-8 w-8 text-purple-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">数据报表</span>
                <span className="text-xs text-gray-500 mt-1">生成报表</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 订单趋势图 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">订单趋势</h3>
          </div>
          <div className="p-6">
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">订单趋势图表</p>
                <p className="text-sm text-gray-400 mt-1">图表组件开发中...</p>
              </div>
            </div>
          </div>
        </div>

        {/* 收入分布图 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">收入分布</h3>
          </div>
          <div className="p-6">
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <PieChart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">收入分布图表</p>
                <p className="text-sm text-gray-400 mt-1">图表组件开发中...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;