import { useState } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Eye, 
  Users, 
  Building2,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  MoreHorizontal,
  FileText,
  Download
} from 'lucide-react';
import { toast } from 'sonner';

interface Enterprise {
  id: string;
  name: string;
  logo: string;
  industry: string;
  scale: string;
  address: string;
  contactPerson: string;
  phone: string;
  email: string;
  status: 'active' | 'inactive' | 'pending' | 'expired';
  contractStart: string;
  contractEnd: string;
  employeeCount: number;
  monthlyBudget: number;
  usedBudget: number;
  totalOrders: number;
  joinDate: string;
  lastActive: string;
}

const EnterpriseManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'pending' | 'expired'>('all');
  const [selectedEnterprise, setSelectedEnterprise] = useState<Enterprise | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // 模拟数据
  const enterprises: Enterprise[] = [
    {
      id: '1',
      name: '科技有限公司',
      logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=technology%20company%20logo%20modern%20blue&image_size=square',
      industry: '互联网科技',
      scale: '500-1000人',
      address: '北京市海淀区中关村软件园',
      contactPerson: '李总',
      phone: '010-88888888',
      email: '<EMAIL>',
      status: 'active',
      contractStart: '2023-01-01',
      contractEnd: '2024-12-31',
      employeeCount: 756,
      monthlyBudget: 150000,
      usedBudget: 128500,
      totalOrders: 12456,
      joinDate: '2023-01-01',
      lastActive: '2024-01-15 16:30'
    },
    {
      id: '2',
      name: '制造集团',
      logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=manufacturing%20company%20logo%20industrial&image_size=square',
      industry: '制造业',
      scale: '1000+人',
      address: '上海市浦东新区张江高科技园区',
      contactPerson: '王经理',
      phone: '021-66666666',
      email: '<EMAIL>',
      status: 'pending',
      contractStart: '2024-02-01',
      contractEnd: '2025-01-31',
      employeeCount: 1200,
      monthlyBudget: 200000,
      usedBudget: 0,
      totalOrders: 0,
      joinDate: '2024-01-10',
      lastActive: '2024-01-14 10:20'
    },
    {
      id: '3',
      name: '金融服务公司',
      logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=financial%20services%20logo%20professional&image_size=square',
      industry: '金融服务',
      scale: '200-500人',
      address: '深圳市福田区金融中心区',
      contactPerson: '张主管',
      phone: '0755-77777777',
      email: '<EMAIL>',
      status: 'expired',
      contractStart: '2022-06-01',
      contractEnd: '2023-12-31',
      employeeCount: 345,
      monthlyBudget: 80000,
      usedBudget: 75600,
      totalOrders: 8901,
      joinDate: '2022-06-01',
      lastActive: '2024-01-05 14:15'
    },
    {
      id: '4',
      name: '教育科技公司',
      logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=education%20technology%20logo%20creative&image_size=square',
      industry: '教育培训',
      scale: '100-200人',
      address: '广州市天河区珠江新城',
      contactPerson: '陈老师',
      phone: '020-55555555',
      email: '<EMAIL>',
      status: 'active',
      contractStart: '2023-09-01',
      contractEnd: '2024-08-31',
      employeeCount: 156,
      monthlyBudget: 45000,
      usedBudget: 38900,
      totalOrders: 3456,
      joinDate: '2023-09-01',
      lastActive: '2024-01-15 11:45'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'expired':
        return <AlertTriangle className="h-4 w-4" />;
      case 'inactive':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '正常服务';
      case 'pending':
        return '待审核';
      case 'expired':
        return '合同到期';
      case 'inactive':
        return '已停用';
      default:
        return '未知';
    }
  };

  const filteredEnterprises = enterprises.filter(enterprise => {
    const matchesSearch = enterprise.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         enterprise.industry.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         enterprise.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || enterprise.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleViewDetails = (enterprise: Enterprise) => {
    setSelectedEnterprise(enterprise);
    setShowDetails(true);
  };

  const getBudgetUsagePercentage = (used: number, total: number) => {
    return Math.round((used / total) * 100);
  };

  const getBudgetUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">企业客户管理</h1>
          <p className="text-gray-600 mt-1">管理平台企业客户信息和合同</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center">
            <Download className="h-4 w-4 mr-2" />
            导出报表
          </button>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Plus className="h-4 w-4 mr-2" />
            添加企业
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总企业数</p>
              <p className="text-2xl font-bold text-gray-900">{enterprises.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">活跃企业</p>
              <p className="text-2xl font-bold text-gray-900">
                {enterprises.filter(e => e.status === 'active').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">即将到期</p>
              <p className="text-2xl font-bold text-gray-900">
                {enterprises.filter(e => e.status === 'expired').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总员工数</p>
              <p className="text-2xl font-bold text-gray-900">
                {enterprises.reduce((sum, e) => sum + e.employeeCount, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="搜索企业名称、行业或联系人..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">全部状态</option>
              <option value="active">正常服务</option>
              <option value="pending">待审核</option>
              <option value="expired">合同到期</option>
              <option value="inactive">已停用</option>
            </select>
            <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="h-4 w-4 mr-2" />
              更多筛选
            </button>
          </div>
        </div>
      </div>

      {/* 企业列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  企业信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  联系方式
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  合同信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  预算使用
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEnterprises.map((enterprise) => {
                const budgetPercentage = getBudgetUsagePercentage(enterprise.usedBudget, enterprise.monthlyBudget);
                
                return (
                  <tr key={enterprise.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <img
                          src={enterprise.logo}
                          alt={enterprise.name}
                          className="h-10 w-10 rounded-lg object-cover"
                        />
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{enterprise.name}</div>
                          <div className="text-sm text-gray-500">{enterprise.industry}</div>
                          <div className="text-xs text-gray-500 flex items-center mt-1">
                            <Users className="h-3 w-3 mr-1" />
                            {enterprise.employeeCount}人 · {enterprise.scale}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{enterprise.contactPerson}</div>
                      <div className="text-sm text-gray-500 flex items-center mt-1">
                        <Phone className="h-3 w-3 mr-1" />
                        {enterprise.phone}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center mt-1">
                        <Mail className="h-3 w-3 mr-1" />
                        {enterprise.email}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(enterprise.status)}`}>
                        {getStatusIcon(enterprise.status)}
                        <span className="ml-1">{getStatusText(enterprise.status)}</span>
                      </span>
                      <div className="text-xs text-gray-500 mt-1">
                        最后活跃：{enterprise.lastActive}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {enterprise.contractStart} ~ {enterprise.contractEnd}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center mt-1">
                        <FileText className="h-3 w-3 mr-1" />
                        {enterprise.totalOrders}订单
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        ¥{enterprise.usedBudget.toLocaleString()} / ¥{enterprise.monthlyBudget.toLocaleString()}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div 
                          className={`h-2 rounded-full ${getBudgetUsageColor(budgetPercentage)}`}
                          style={{ width: `${budgetPercentage}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-500 mt-1">{budgetPercentage}% 已使用</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewDetails(enterprise)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          <MoreHorizontal className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* 企业详情弹窗 */}
      {showDetails && selectedEnterprise && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowDetails(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">企业详情</h3>
              </div>
              
              <div className="bg-white px-6 py-4 space-y-6">
                <div className="flex items-center space-x-4">
                  <img
                    src={selectedEnterprise.logo}
                    alt={selectedEnterprise.name}
                    className="h-16 w-16 rounded-lg object-cover"
                  />
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">{selectedEnterprise.name}</h4>
                    <p className="text-gray-600">{selectedEnterprise.industry}</p>
                    <div className="flex items-center mt-2">
                      <Users className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="text-sm text-gray-600">
                        {selectedEnterprise.employeeCount}员工 · {selectedEnterprise.scale}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-3">基本信息</h5>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm">
                        <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">{selectedEnterprise.address}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Phone className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">{selectedEnterprise.phone}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Mail className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">{selectedEnterprise.email}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-3">合同信息</h5>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">合同开始：</span>
                        <span className="font-medium">{selectedEnterprise.contractStart}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">合同结束：</span>
                        <span className="font-medium">{selectedEnterprise.contractEnd}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">入驻时间：</span>
                        <span className="font-medium">{selectedEnterprise.joinDate}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-3">消费数据</h5>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">月预算：</span>
                        <span className="font-medium">¥{selectedEnterprise.monthlyBudget.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">已使用：</span>
                        <span className="font-medium">¥{selectedEnterprise.usedBudget.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">总订单：</span>
                        <span className="font-medium">{selectedEnterprise.totalOrders}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                <button
                  onClick={() => setShowDetails(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  关闭
                </button>
                <button className="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700">
                  编辑企业
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnterpriseManagement;