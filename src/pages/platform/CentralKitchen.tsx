import { useState } from 'react';
import { 
  Search, 
  Filter, 
  ChefHat, 
  Package, 
  Thermometer, 
  Clock, 
  Users, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  MoreHorizontal,
  MapPin,
  Truck,
  Activity,
  BarChart3,
  Settings,
  Calendar,
  FileText,
  Shield
} from 'lucide-react';
import { toast } from 'sonner';

interface Kitchen {
  id: string;
  name: string;
  address: string;
  manager: string;
  phone: string;
  status: 'active' | 'inactive' | 'maintenance';
  capacity: {
    daily: number;
    current: number;
  };
  equipment: {
    total: number;
    working: number;
    maintenance: number;
  };
  staff: {
    total: number;
    present: number;
    absent: number;
  };
  temperature: {
    storage: number;
    cooking: number;
    serving: number;
  };
  hygiene: {
    score: number;
    lastInspection: string;
    nextInspection: string;
  };
  production: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  efficiency: number;
  createdAt: string;
}

interface ProductionOrder {
  id: string;
  orderNumber: string;
  kitchenId: string;
  kitchenName: string;
  items: {
    name: string;
    quantity: number;
    unit: string;
  }[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  estimatedTime: number; // minutes
  actualTime?: number; // minutes
  assignedChef: string;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  qualityCheck: {
    passed: boolean;
    inspector: string;
    notes?: string;
  } | null;
}

interface KitchenStats {
  totalKitchens: number;
  activeKitchens: number;
  totalProduction: number;
  avgEfficiency: number;
  qualityScore: number;
  totalStaff: number;
  equipmentUptime: number;
  dailyCapacity: number;
}

const CentralKitchen = () => {
  const [activeTab, setActiveTab] = useState<'kitchens' | 'production' | 'quality' | 'analytics'>('kitchens');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'maintenance'>('all');
  const [productionStatusFilter, setProductionStatusFilter] = useState<'all' | 'pending' | 'in_progress' | 'completed' | 'cancelled'>('all');
  const [selectedKitchen, setSelectedKitchen] = useState<Kitchen | null>(null);
  const [showKitchenDetail, setShowKitchenDetail] = useState(false);

  // 模拟中央厨房统计数据
  const kitchenStats: KitchenStats = {
    totalKitchens: 12,
    activeKitchens: 10,
    totalProduction: 15680,
    avgEfficiency: 87.5,
    qualityScore: 96.2,
    totalStaff: 156,
    equipmentUptime: 94.8,
    dailyCapacity: 18000
  };

  // 模拟中央厨房数据
  const kitchens: Kitchen[] = [
    {
      id: '1',
      name: '朝阳中央厨房',
      address: '北京市朝阳区建国门外大街88号',
      manager: '张主厨',
      phone: '13800138001',
      status: 'active',
      capacity: {
        daily: 2000,
        current: 1650
      },
      equipment: {
        total: 25,
        working: 23,
        maintenance: 2
      },
      staff: {
        total: 18,
        present: 16,
        absent: 2
      },
      temperature: {
        storage: 4.2,
        cooking: 180,
        serving: 65
      },
      hygiene: {
        score: 98.5,
        lastInspection: '2024-01-10',
        nextInspection: '2024-02-10'
      },
      production: {
        today: 1650,
        thisWeek: 9800,
        thisMonth: 42500
      },
      efficiency: 92.3,
      createdAt: '2023-03-15'
    },
    {
      id: '2',
      name: '海淀中央厨房',
      address: '北京市海淀区中关村大街128号',
      manager: '李主厨',
      phone: '13800138002',
      status: 'active',
      capacity: {
        daily: 1800,
        current: 1420
      },
      equipment: {
        total: 22,
        working: 20,
        maintenance: 2
      },
      staff: {
        total: 15,
        present: 14,
        absent: 1
      },
      temperature: {
        storage: 3.8,
        cooking: 175,
        serving: 68
      },
      hygiene: {
        score: 96.8,
        lastInspection: '2024-01-12',
        nextInspection: '2024-02-12'
      },
      production: {
        today: 1420,
        thisWeek: 8540,
        thisMonth: 38200
      },
      efficiency: 88.9,
      createdAt: '2023-05-20'
    },
    {
      id: '3',
      name: '西城中央厨房',
      address: '北京市西城区西单北大街66号',
      manager: '王主厨',
      phone: '13800138003',
      status: 'maintenance',
      capacity: {
        daily: 1500,
        current: 0
      },
      equipment: {
        total: 20,
        working: 15,
        maintenance: 5
      },
      staff: {
        total: 12,
        present: 8,
        absent: 4
      },
      temperature: {
        storage: 5.1,
        cooking: 0,
        serving: 0
      },
      hygiene: {
        score: 94.2,
        lastInspection: '2024-01-08',
        nextInspection: '2024-02-08'
      },
      production: {
        today: 0,
        thisWeek: 4200,
        thisMonth: 28500
      },
      efficiency: 0,
      createdAt: '2023-07-10'
    }
  ];

  // 模拟生产订单数据
  const productionOrders: ProductionOrder[] = [
    {
      id: '1',
      orderNumber: 'PRD-2024-001247',
      kitchenId: '1',
      kitchenName: '朝阳中央厨房',
      items: [
        { name: '宫保鸡丁', quantity: 50, unit: '份' },
        { name: '麻婆豆腐', quantity: 30, unit: '份' },
        { name: '米饭', quantity: 80, unit: '份' }
      ],
      priority: 'high',
      status: 'in_progress',
      estimatedTime: 45,
      assignedChef: '张师傅',
      createdAt: '2024-01-15 13:30',
      startedAt: '2024-01-15 13:35',
      qualityCheck: null
    },
    {
      id: '2',
      orderNumber: 'PRD-2024-001246',
      kitchenId: '2',
      kitchenName: '海淀中央厨房',
      items: [
        { name: '红烧肉', quantity: 40, unit: '份' },
        { name: '青菜豆腐汤', quantity: 40, unit: '份' }
      ],
      priority: 'medium',
      status: 'completed',
      estimatedTime: 60,
      actualTime: 55,
      assignedChef: '李师傅',
      createdAt: '2024-01-15 12:00',
      startedAt: '2024-01-15 12:05',
      completedAt: '2024-01-15 13:00',
      qualityCheck: {
        passed: true,
        inspector: '质检员A',
        notes: '质量优秀，符合标准'
      }
    },
    {
      id: '3',
      orderNumber: 'PRD-2024-001245',
      kitchenId: '1',
      kitchenName: '朝阳中央厨房',
      items: [
        { name: '糖醋里脊', quantity: 60, unit: '份' },
        { name: '蛋花汤', quantity: 60, unit: '份' },
        { name: '米饭', quantity: 60, unit: '份' }
      ],
      priority: 'urgent',
      status: 'pending',
      estimatedTime: 50,
      assignedChef: '张师傅',
      createdAt: '2024-01-15 14:20',
      qualityCheck: null
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '运营中';
      case 'inactive':
        return '停业';
      case 'maintenance':
        return '维护中';
      default:
        return '未知';
    }
  };

  const getProductionStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProductionStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待开始';
      case 'in_progress':
        return '制作中';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return '紧急';
      case 'high':
        return '高';
      case 'medium':
        return '中';
      case 'low':
        return '低';
      default:
        return '未知';
    }
  };

  const getTemperatureStatus = (temp: number, type: 'storage' | 'cooking' | 'serving') => {
    switch (type) {
      case 'storage':
        return temp <= 5 ? 'normal' : 'warning';
      case 'cooking':
        return temp >= 160 && temp <= 200 ? 'normal' : 'warning';
      case 'serving':
        return temp >= 60 && temp <= 75 ? 'normal' : 'warning';
      default:
        return 'normal';
    }
  };

  const filteredKitchens = kitchens.filter(kitchen => {
    const matchesSearch = kitchen.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         kitchen.manager.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         kitchen.address.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || kitchen.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredOrders = productionOrders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.kitchenName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.assignedChef.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = productionStatusFilter === 'all' || order.status === productionStatusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleViewKitchen = (kitchen: Kitchen) => {
    setSelectedKitchen(kitchen);
    setShowKitchenDetail(true);
  };

  const handleUpdateKitchenStatus = (kitchenId: string, status: string) => {
    toast.success(`厨房状态已更新为${getStatusText(status)}`);
  };

  const handleStartProduction = (orderId: string) => {
    toast.success('生产订单已开始制作');
  };

  const handleCompleteProduction = (orderId: string) => {
    toast.success('生产订单已完成');
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">中央厨房管理</h1>
          <p className="text-gray-600 mt-1">管理中央厨房运营和生产</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <ChefHat className="h-4 w-4 mr-2" />
            添加厨房
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <Package className="h-4 w-4 mr-2" />
            新建生产单
          </button>
        </div>
      </div>

      {/* 中央厨房统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">厨房总数</p>
              <p className="text-2xl font-bold text-gray-900">{kitchenStats.totalKitchens}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <ChefHat className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">运营中: {kitchenStats.activeKitchens}</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">今日产量</p>
              <p className="text-2xl font-bold text-gray-900">{kitchenStats.totalProduction.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Package className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+12.5%</span>
            <span className="text-sm text-gray-500 ml-2">vs 昨日</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均效率</p>
              <p className="text-2xl font-bold text-gray-900">{kitchenStats.avgEfficiency}%</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Activity className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">目标: &gt;85%</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">质量评分</p>
              <p className="text-2xl font-bold text-gray-900">{kitchenStats.qualityScore}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Shield className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span className="text-sm text-gray-500 ml-1">优秀</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">员工总数</p>
              <p className="text-2xl font-bold text-gray-900">{kitchenStats.totalStaff}</p>
            </div>
            <div className="p-3 bg-indigo-100 rounded-lg">
              <Users className="h-6 w-6 text-indigo-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">分布在{kitchenStats.totalKitchens}个厨房</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">设备运行率</p>
              <p className="text-2xl font-bold text-gray-900">{kitchenStats.equipmentUptime}%</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Settings className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">设备状态良好</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">日产能</p>
              <p className="text-2xl font-bold text-gray-900">{kitchenStats.dailyCapacity.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <BarChart3 className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">利用率: {Math.round((kitchenStats.totalProduction / kitchenStats.dailyCapacity) * 100)}%</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">配送覆盖</p>
              <p className="text-2xl font-bold text-gray-900">15km</p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <Truck className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">平均配送半径</span>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('kitchens')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'kitchens'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              厨房管理
            </button>
            <button
              onClick={() => setActiveTab('production')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'production'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              生产管理
            </button>
            <button
              onClick={() => setActiveTab('quality')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'quality'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              质量管理
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'analytics'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              数据分析
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* 搜索和筛选 */}
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder={activeTab === 'kitchens' ? '搜索厨房名称、管理员或地址...' : '搜索生产单号、厨房或厨师...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              {activeTab === 'kitchens' ? (
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">全部状态</option>
                  <option value="active">运营中</option>
                  <option value="inactive">停业</option>
                  <option value="maintenance">维护中</option>
                </select>
              ) : activeTab === 'production' ? (
                <select
                  value={productionStatusFilter}
                  onChange={(e) => setProductionStatusFilter(e.target.value as any)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">全部状态</option>
                  <option value="pending">待开始</option>
                  <option value="in_progress">制作中</option>
                  <option value="completed">已完成</option>
                  <option value="cancelled">已取消</option>
                </select>
              ) : null}
            </div>
          </div>

          {/* 厨房管理 */}
          {activeTab === 'kitchens' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      厨房信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      产能/效率
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      人员/设备
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      温度监控
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      卫生评分
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredKitchens.map((kitchen) => (
                    <tr key={kitchen.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{kitchen.name}</div>
                          <div className="text-sm text-gray-600">{kitchen.manager}</div>
                          <div className="text-xs text-gray-500">{kitchen.address}</div>
                          <div className="text-xs text-gray-500">{kitchen.phone}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(kitchen.status)}`}>
                          {getStatusText(kitchen.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {kitchen.capacity.current}/{kitchen.capacity.daily}
                        </div>
                        <div className="text-xs text-gray-500">
                          利用率: {Math.round((kitchen.capacity.current / kitchen.capacity.daily) * 100)}%
                        </div>
                        <div className="text-xs text-gray-500">
                          效率: {kitchen.efficiency}%
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          人员: {kitchen.staff.present}/{kitchen.staff.total}
                        </div>
                        <div className="text-xs text-gray-500">
                          设备: {kitchen.equipment.working}/{kitchen.equipment.total}
                        </div>
                        {kitchen.equipment.maintenance > 0 && (
                          <div className="text-xs text-yellow-600">
                            维护中: {kitchen.equipment.maintenance}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <div className={`text-xs ${
                            getTemperatureStatus(kitchen.temperature.storage, 'storage') === 'normal' 
                              ? 'text-green-600' : 'text-red-600'
                          }`}>
                            冷藏: {kitchen.temperature.storage}°C
                          </div>
                          <div className={`text-xs ${
                            getTemperatureStatus(kitchen.temperature.cooking, 'cooking') === 'normal' 
                              ? 'text-green-600' : 'text-red-600'
                          }`}>
                            烹饪: {kitchen.temperature.cooking}°C
                          </div>
                          <div className={`text-xs ${
                            getTemperatureStatus(kitchen.temperature.serving, 'serving') === 'normal' 
                              ? 'text-green-600' : 'text-red-600'
                          }`}>
                            保温: {kitchen.temperature.serving}°C
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{kitchen.hygiene.score}</div>
                        <div className="text-xs text-gray-500">
                          上次检查: {kitchen.hygiene.lastInspection}
                        </div>
                        <div className="text-xs text-gray-500">
                          下次检查: {kitchen.hygiene.nextInspection}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewKitchen(kitchen)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <ChefHat className="h-4 w-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            <Thermometer className="h-4 w-4" />
                          </button>
                          <button className="text-purple-600 hover:text-purple-900">
                            <Settings className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            <MoreHorizontal className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 生产管理 */}
          {activeTab === 'production' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      生产单信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      厨房/厨师
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      生产项目
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态/优先级
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      质检
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredOrders.map((order) => (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{order.orderNumber}</div>
                          <div className="text-xs text-gray-500">{order.createdAt}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{order.kitchenName}</div>
                          <div className="text-sm text-gray-600">{order.assignedChef}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          {order.items.slice(0, 2).map((item, index) => (
                            <div key={index} className="text-xs text-gray-600">
                              {item.name} x{item.quantity}{item.unit}
                            </div>
                          ))}
                          {order.items.length > 2 && (
                            <div className="text-xs text-gray-500">
                              +{order.items.length - 2}项...
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProductionStatusColor(order.status)}`}>
                            {getProductionStatusText(order.status)}
                          </span>
                          <br />
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(order.priority)}`}>
                            {getPriorityText(order.priority)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          预计: {order.estimatedTime}分钟
                        </div>
                        {order.actualTime && (
                          <div className="text-xs text-gray-500">
                            实际: {order.actualTime}分钟
                          </div>
                        )}
                        {order.startedAt && (
                          <div className="text-xs text-gray-500">
                            开始: {order.startedAt}
                          </div>
                        )}
                        {order.completedAt && (
                          <div className="text-xs text-gray-500">
                            完成: {order.completedAt}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {order.qualityCheck ? (
                          <div>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              order.qualityCheck.passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {order.qualityCheck.passed ? '通过' : '不通过'}
                            </span>
                            <div className="text-xs text-gray-500 mt-1">
                              {order.qualityCheck.inspector}
                            </div>
                          </div>
                        ) : (
                          <span className="text-xs text-gray-500">待检查</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          {order.status === 'pending' && (
                            <button
                              onClick={() => handleStartProduction(order.id)}
                              className="text-green-600 hover:text-green-900"
                            >
                              <Clock className="h-4 w-4" />
                            </button>
                          )}
                          {order.status === 'in_progress' && (
                            <button
                              onClick={() => handleCompleteProduction(order.id)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </button>
                          )}
                          <button className="text-purple-600 hover:text-purple-900">
                            <FileText className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            <MoreHorizontal className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 质量管理 */}
          {activeTab === 'quality' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">质量管理系统</h3>
                <p className="text-gray-500">食品安全检查、卫生监控、质量评估等功能</p>
                <p className="text-sm text-gray-400 mt-2">功能开发中...</p>
              </div>
            </div>
          )}

          {/* 数据分析 */}
          {activeTab === 'analytics' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">数据分析中心</h3>
                <p className="text-gray-500">生产效率分析、成本控制、趋势预测等数据图表</p>
                <p className="text-sm text-gray-400 mt-2">功能开发中...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 厨房详情弹窗 */}
      {showKitchenDetail && selectedKitchen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowKitchenDetail(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">厨房详情</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedKitchen.status)}`}>
                    {getStatusText(selectedKitchen.status)}
                  </span>
                </div>
              </div>
              
              <div className="bg-white px-6 py-4 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">基本信息</h4>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-700">厨房名称：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedKitchen.name}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">管理员：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedKitchen.manager}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">联系电话：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedKitchen.phone}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">地址：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedKitchen.address}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">创建时间：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedKitchen.createdAt}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-lg font-semibold text-gray-900 mb-4">运营数据</h5>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">日产能：</span>
                        <span className="text-sm font-medium">{selectedKitchen.capacity.daily}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">当前产量：</span>
                        <span className="text-sm font-medium">{selectedKitchen.capacity.current}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">利用率：</span>
                        <span className="text-sm font-medium">
                          {Math.round((selectedKitchen.capacity.current / selectedKitchen.capacity.daily) * 100)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">效率：</span>
                        <span className="text-sm font-medium">{selectedKitchen.efficiency}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">卫生评分：</span>
                        <span className="text-sm font-medium">{selectedKitchen.hygiene.score}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="text-lg font-semibold text-gray-900 mb-4">人员与设备</h5>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h6 className="text-sm font-medium text-gray-900 mb-2">人员状况</h6>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">总人数：</span>
                          <span className="font-medium">{selectedKitchen.staff.total}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">在岗：</span>
                          <span className="font-medium text-green-600">{selectedKitchen.staff.present}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">缺勤：</span>
                          <span className="font-medium text-red-600">{selectedKitchen.staff.absent}</span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h6 className="text-sm font-medium text-gray-900 mb-2">设备状况</h6>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">总设备：</span>
                          <span className="font-medium">{selectedKitchen.equipment.total}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">正常：</span>
                          <span className="font-medium text-green-600">{selectedKitchen.equipment.working}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">维护：</span>
                          <span className="font-medium text-yellow-600">{selectedKitchen.equipment.maintenance}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="text-lg font-semibold text-gray-900 mb-4">温度监控</h5>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className={`text-2xl font-bold ${
                        getTemperatureStatus(selectedKitchen.temperature.storage, 'storage') === 'normal' 
                          ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {selectedKitchen.temperature.storage}°C
                      </div>
                      <div className="text-sm text-gray-600">冷藏温度</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className={`text-2xl font-bold ${
                        getTemperatureStatus(selectedKitchen.temperature.cooking, 'cooking') === 'normal' 
                          ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {selectedKitchen.temperature.cooking}°C
                      </div>
                      <div className="text-sm text-gray-600">烹饪温度</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className={`text-2xl font-bold ${
                        getTemperatureStatus(selectedKitchen.temperature.serving, 'serving') === 'normal' 
                          ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {selectedKitchen.temperature.serving}°C
                      </div>
                      <div className="text-sm text-gray-600">保温温度</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="text-lg font-semibold text-gray-900 mb-4">生产统计</h5>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">{selectedKitchen.production.today}</div>
                      <div className="text-sm text-gray-600">今日产量</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">{selectedKitchen.production.thisWeek}</div>
                      <div className="text-sm text-gray-600">本周产量</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">{selectedKitchen.production.thisMonth}</div>
                      <div className="text-sm text-gray-600">本月产量</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                <button
                  onClick={() => setShowKitchenDetail(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  关闭
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                  编辑信息
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CentralKitchen;