import { useState } from 'react';
import { 
  Search, 
  Filter, 
  MapPin, 
  Building2, 
  Users, 
  TrendingUp, 
  Package, 
  DollarSign, 
  Clock, 
  Star, 
  MoreHorizontal,
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  BarChart3,
  Activity,
  Target,
  Truck
} from 'lucide-react';
import { toast } from 'sonner';

interface Region {
  id: string;
  name: string;
  code: string;
  manager: string;
  phone: string;
  email: string;
  status: 'active' | 'inactive' | 'planning';
  area: {
    coordinates: {
      lat: number;
      lng: number;
    }[];
    center: {
      lat: number;
      lng: number;
    };
    radius: number; // km
  };
  coverage: {
    merchants: number;
    enterprises: number;
    deliveryPersons: number;
    kitchens: number;
  };
  performance: {
    dailyOrders: number;
    weeklyOrders: number;
    monthlyOrders: number;
    revenue: number;
    avgDeliveryTime: number;
    customerSatisfaction: number;
  };
  demographics: {
    population: number;
    businessDistricts: number;
    residentialAreas: number;
    schools: number;
    offices: number;
  };
  logistics: {
    deliveryHubs: number;
    avgDistance: number;
    trafficLevel: 'low' | 'medium' | 'high';
    peakHours: string[];
  };
  createdAt: string;
  updatedAt: string;
}

interface RegionStats {
  totalRegions: number;
  activeRegions: number;
  totalCoverage: number;
  avgPerformance: number;
  totalRevenue: number;
  totalOrders: number;
  avgSatisfaction: number;
  expansionOpportunities: number;
}

const RegionManagement = () => {
  const [activeTab, setActiveTab] = useState<'regions' | 'coverage' | 'performance' | 'analytics'>('regions');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'planning'>('all');
  const [selectedRegion, setSelectedRegion] = useState<Region | null>(null);
  const [showRegionDetail, setShowRegionDetail] = useState(false);
  const [showAddRegion, setShowAddRegion] = useState(false);

  // 模拟区域统计数据
  const regionStats: RegionStats = {
    totalRegions: 15,
    activeRegions: 12,
    totalCoverage: 2580, // km²
    avgPerformance: 87.3,
    totalRevenue: 1250000,
    totalOrders: 45680,
    avgSatisfaction: 4.6,
    expansionOpportunities: 8
  };

  // 模拟区域数据
  const regions: Region[] = [
    {
      id: '1',
      name: '朝阳商务区',
      code: 'CY-001',
      manager: '张经理',
      phone: '13800138001',
      email: '<EMAIL>',
      status: 'active',
      area: {
        coordinates: [
          { lat: 39.9042, lng: 116.4074 },
          { lat: 39.9142, lng: 116.4174 },
          { lat: 39.9242, lng: 116.4274 },
          { lat: 39.9342, lng: 116.4374 }
        ],
        center: { lat: 39.9192, lng: 116.4224 },
        radius: 5.2
      },
      coverage: {
        merchants: 156,
        enterprises: 89,
        deliveryPersons: 45,
        kitchens: 3
      },
      performance: {
        dailyOrders: 1247,
        weeklyOrders: 8729,
        monthlyOrders: 37456,
        revenue: 285600,
        avgDeliveryTime: 28,
        customerSatisfaction: 4.7
      },
      demographics: {
        population: 125000,
        businessDistricts: 8,
        residentialAreas: 12,
        schools: 5,
        offices: 156
      },
      logistics: {
        deliveryHubs: 2,
        avgDistance: 3.2,
        trafficLevel: 'high',
        peakHours: ['11:30-13:30', '17:30-19:30']
      },
      createdAt: '2023-03-15',
      updatedAt: '2024-01-15'
    },
    {
      id: '2',
      name: '海淀科技园',
      code: 'HD-002',
      manager: '李经理',
      phone: '***********',
      email: '<EMAIL>',
      status: 'active',
      area: {
        coordinates: [
          { lat: 39.9842, lng: 116.3074 },
          { lat: 39.9942, lng: 116.3174 },
          { lat: 40.0042, lng: 116.3274 },
          { lat: 40.0142, lng: 116.3374 }
        ],
        center: { lat: 39.9992, lng: 116.3224 },
        radius: 4.8
      },
      coverage: {
        merchants: 134,
        enterprises: 156,
        deliveryPersons: 38,
        kitchens: 2
      },
      performance: {
        dailyOrders: 1089,
        weeklyOrders: 7623,
        monthlyOrders: 32678,
        revenue: 245800,
        avgDeliveryTime: 25,
        customerSatisfaction: 4.8
      },
      demographics: {
        population: 98000,
        businessDistricts: 6,
        residentialAreas: 8,
        schools: 12,
        offices: 234
      },
      logistics: {
        deliveryHubs: 2,
        avgDistance: 2.8,
        trafficLevel: 'medium',
        peakHours: ['12:00-14:00', '18:00-20:00']
      },
      createdAt: '2023-05-20',
      updatedAt: '2024-01-14'
    },
    {
      id: '3',
      name: '西城金融街',
      code: 'XC-003',
      manager: '王经理',
      phone: '***********',
      email: '<EMAIL>',
      status: 'active',
      area: {
        coordinates: [
          { lat: 39.9142, lng: 116.3574 },
          { lat: 39.9242, lng: 116.3674 },
          { lat: 39.9342, lng: 116.3774 },
          { lat: 39.9442, lng: 116.3874 }
        ],
        center: { lat: 39.9292, lng: 116.3724 },
        radius: 3.5
      },
      coverage: {
        merchants: 98,
        enterprises: 234,
        deliveryPersons: 28,
        kitchens: 1
      },
      performance: {
        dailyOrders: 856,
        weeklyOrders: 5992,
        monthlyOrders: 25678,
        revenue: 198400,
        avgDeliveryTime: 22,
        customerSatisfaction: 4.9
      },
      demographics: {
        population: 67000,
        businessDistricts: 12,
        residentialAreas: 4,
        schools: 2,
        offices: 345
      },
      logistics: {
        deliveryHubs: 1,
        avgDistance: 2.1,
        trafficLevel: 'high',
        peakHours: ['11:30-13:30', '17:00-19:00']
      },
      createdAt: '2023-07-10',
      updatedAt: '2024-01-13'
    },
    {
      id: '4',
      name: '通州新城',
      code: 'TZ-004',
      manager: '赵经理',
      phone: '***********',
      email: '<EMAIL>',
      status: 'planning',
      area: {
        coordinates: [
          { lat: 39.9042, lng: 116.6574 },
          { lat: 39.9142, lng: 116.6674 },
          { lat: 39.9242, lng: 116.6774 },
          { lat: 39.9342, lng: 116.6874 }
        ],
        center: { lat: 39.9192, lng: 116.6724 },
        radius: 6.8
      },
      coverage: {
        merchants: 0,
        enterprises: 0,
        deliveryPersons: 0,
        kitchens: 0
      },
      performance: {
        dailyOrders: 0,
        weeklyOrders: 0,
        monthlyOrders: 0,
        revenue: 0,
        avgDeliveryTime: 0,
        customerSatisfaction: 0
      },
      demographics: {
        population: 156000,
        businessDistricts: 3,
        residentialAreas: 25,
        schools: 8,
        offices: 45
      },
      logistics: {
        deliveryHubs: 0,
        avgDistance: 0,
        trafficLevel: 'low',
        peakHours: []
      },
      createdAt: '2024-01-10',
      updatedAt: '2024-01-15'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'planning':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '运营中';
      case 'inactive':
        return '暂停';
      case 'planning':
        return '规划中';
      default:
        return '未知';
    }
  };

  const getTrafficLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrafficLevelText = (level: string) => {
    switch (level) {
      case 'low':
        return '低';
      case 'medium':
        return '中';
      case 'high':
        return '高';
      default:
        return '未知';
    }
  };

  const filteredRegions = regions.filter(region => {
    const matchesSearch = region.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         region.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         region.manager.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || region.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleViewRegion = (region: Region) => {
    setSelectedRegion(region);
    setShowRegionDetail(true);
  };

  const handleUpdateRegionStatus = (regionId: string, status: string) => {
    toast.success(`区域状态已更新为${getStatusText(status)}`);
  };

  const handleDeleteRegion = (regionId: string) => {
    toast.success('区域已删除');
  };

  const handleAddRegion = () => {
    setShowAddRegion(true);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">区域管理</h1>
          <p className="text-gray-600 mt-1">管理服务区域和覆盖范围</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button 
            onClick={handleAddRegion}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加区域
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <MapPin className="h-4 w-4 mr-2" />
            地图视图
          </button>
        </div>
      </div>

      {/* 区域统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总区域数</p>
              <p className="text-2xl font-bold text-gray-900">{regionStats.totalRegions}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <MapPin className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">运营中: {regionStats.activeRegions}</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">覆盖面积</p>
              <p className="text-2xl font-bold text-gray-900">{regionStats.totalCoverage}km²</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Target className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+15.2%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均绩效</p>
              <p className="text-2xl font-bold text-gray-900">{regionStats.avgPerformance}%</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Activity className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">目标: &gt;85%</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总收入</p>
              <p className="text-2xl font-bold text-gray-900">¥{(regionStats.totalRevenue / 10000).toFixed(1)}万</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+18.7%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总订单量</p>
              <p className="text-2xl font-bold text-gray-900">{regionStats.totalOrders.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-indigo-100 rounded-lg">
              <Package className="h-6 w-6 text-indigo-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+12.3%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">客户满意度</p>
              <p className="text-2xl font-bold text-gray-900">{regionStats.avgSatisfaction}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Star className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <Star className="h-4 w-4 text-yellow-500 fill-current" />
            <span className="text-sm text-gray-500 ml-1">5分制评分</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">扩张机会</p>
              <p className="text-2xl font-bold text-gray-900">{regionStats.expansionOpportunities}</p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">潜在区域</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">配送网络</p>
              <p className="text-2xl font-bold text-gray-900">98.5%</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Truck className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">网络覆盖率</span>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('regions')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'regions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              区域列表
            </button>
            <button
              onClick={() => setActiveTab('coverage')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'coverage'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              覆盖分析
            </button>
            <button
              onClick={() => setActiveTab('performance')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'performance'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              绩效对比
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'analytics'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              数据分析
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* 搜索和筛选 */}
          <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="搜索区域名称、编码或管理员..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">全部状态</option>
                <option value="active">运营中</option>
                <option value="inactive">暂停</option>
                <option value="planning">规划中</option>
              </select>
            </div>
          </div>

          {/* 区域列表 */}
          {activeTab === 'regions' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      区域信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      覆盖范围
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      业务数据
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      绩效指标
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      物流信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRegions.map((region) => (
                    <tr key={region.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{region.name}</div>
                          <div className="text-sm text-gray-600">{region.code}</div>
                          <div className="text-xs text-gray-500">{region.manager}</div>
                          <div className="text-xs text-gray-500">{region.phone}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(region.status)}`}>
                          {getStatusText(region.status)}
                        </span>
                        <div className="text-xs text-gray-500 mt-1">
                          更新: {region.updatedAt}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          半径: {region.area.radius}km
                        </div>
                        <div className="text-xs text-gray-500">
                          中心: {region.area.center.lat.toFixed(4)}, {region.area.center.lng.toFixed(4)}
                        </div>
                        <div className="text-xs text-gray-500">
                          人口: {region.demographics.population.toLocaleString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          商户: {region.coverage.merchants}
                        </div>
                        <div className="text-xs text-gray-500">
                          企业: {region.coverage.enterprises}
                        </div>
                        <div className="text-xs text-gray-500">
                          配送员: {region.coverage.deliveryPersons}
                        </div>
                        <div className="text-xs text-gray-500">
                          厨房: {region.coverage.kitchens}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          日订单: {region.performance.dailyOrders}
                        </div>
                        <div className="text-xs text-gray-500">
                          收入: ¥{region.performance.revenue.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-500">
                          配送: {region.performance.avgDeliveryTime}分钟
                        </div>
                        <div className="flex items-center text-xs text-gray-500">
                          <Star className="h-3 w-3 text-yellow-400 fill-current mr-1" />
                          {region.performance.customerSatisfaction}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          配送点: {region.logistics.deliveryHubs}
                        </div>
                        <div className="text-xs text-gray-500">
                          平均距离: {region.logistics.avgDistance}km
                        </div>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getTrafficLevelColor(region.logistics.trafficLevel)}`}>
                          交通{getTrafficLevelText(region.logistics.trafficLevel)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewRegion(region)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            <Edit className="h-4 w-4" />
                          </button>
                          <button className="text-purple-600 hover:text-purple-900">
                            <MapPin className="h-4 w-4" />
                          </button>
                          <button 
                            onClick={() => handleDeleteRegion(region.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            <MoreHorizontal className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* 覆盖分析 */}
          {activeTab === 'coverage' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">覆盖分析</h3>
                <p className="text-gray-500">区域覆盖热力图、空白区域分析、扩张建议等</p>
                <p className="text-sm text-gray-400 mt-2">功能开发中...</p>
              </div>
            </div>
          )}

          {/* 绩效对比 */}
          {activeTab === 'performance' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">绩效对比</h3>
                <p className="text-gray-500">各区域绩效对比、排名分析、改进建议等</p>
                <p className="text-sm text-gray-400 mt-2">功能开发中...</p>
              </div>
            </div>
          )}

          {/* 数据分析 */}
          {activeTab === 'analytics' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">数据分析</h3>
                <p className="text-gray-500">区域发展趋势、市场潜力分析、投资回报率等</p>
                <p className="text-sm text-gray-400 mt-2">功能开发中...</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 区域详情弹窗 */}
      {showRegionDetail && selectedRegion && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowRegionDetail(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
              <div className="bg-white px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">区域详情 - {selectedRegion.name}</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedRegion.status)}`}>
                    {getStatusText(selectedRegion.status)}
                  </span>
                </div>
              </div>
              
              <div className="bg-white px-6 py-4 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">基本信息</h4>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm font-medium text-gray-700">区域名称：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedRegion.name}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">区域编码：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedRegion.code}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">管理员：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedRegion.manager}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">联系电话：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedRegion.phone}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">邮箱：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedRegion.email}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">创建时间：</span>
                        <span className="text-sm text-gray-600 ml-2">{selectedRegion.createdAt}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-lg font-semibold text-gray-900 mb-4">覆盖信息</h5>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">覆盖半径：</span>
                        <span className="text-sm font-medium">{selectedRegion.area.radius}km</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">商户数量：</span>
                        <span className="text-sm font-medium">{selectedRegion.coverage.merchants}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">企业客户：</span>
                        <span className="text-sm font-medium">{selectedRegion.coverage.enterprises}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">配送员：</span>
                        <span className="text-sm font-medium">{selectedRegion.coverage.deliveryPersons}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">中央厨房：</span>
                        <span className="text-sm font-medium">{selectedRegion.coverage.kitchens}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-lg font-semibold text-gray-900 mb-4">人口统计</h5>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">总人口：</span>
                        <span className="text-sm font-medium">{selectedRegion.demographics.population.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">商业区：</span>
                        <span className="text-sm font-medium">{selectedRegion.demographics.businessDistricts}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">住宅区：</span>
                        <span className="text-sm font-medium">{selectedRegion.demographics.residentialAreas}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">学校：</span>
                        <span className="text-sm font-medium">{selectedRegion.demographics.schools}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">办公楼：</span>
                        <span className="text-sm font-medium">{selectedRegion.demographics.offices}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="text-lg font-semibold text-gray-900 mb-4">绩效数据</h5>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">{selectedRegion.performance.dailyOrders}</div>
                      <div className="text-sm text-gray-600">日订单量</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">¥{selectedRegion.performance.revenue.toLocaleString()}</div>
                      <div className="text-sm text-gray-600">月收入</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">{selectedRegion.performance.avgDeliveryTime}分钟</div>
                      <div className="text-sm text-gray-600">平均配送时间</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-gray-900">{selectedRegion.performance.customerSatisfaction}</div>
                      <div className="text-sm text-gray-600">客户满意度</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="text-lg font-semibold text-gray-900 mb-4">物流信息</h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">配送中心：</span>
                        <span className="text-sm font-medium">{selectedRegion.logistics.deliveryHubs}个</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">平均配送距离：</span>
                        <span className="text-sm font-medium">{selectedRegion.logistics.avgDistance}km</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">交通状况：</span>
                        <span className={`text-sm font-medium px-2 py-1 rounded ${getTrafficLevelColor(selectedRegion.logistics.trafficLevel)}`}>
                          {getTrafficLevelText(selectedRegion.logistics.trafficLevel)}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-2">高峰时段：</div>
                      <div className="space-y-1">
                        {selectedRegion.logistics.peakHours.length > 0 ? (
                          selectedRegion.logistics.peakHours.map((time, index) => (
                            <div key={index} className="text-sm text-gray-600 bg-gray-50 px-2 py-1 rounded">
                              {time}
                            </div>
                          ))
                        ) : (
                          <div className="text-sm text-gray-500">暂无数据</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                <button
                  onClick={() => setShowRegionDetail(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  关闭
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                  编辑区域
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 添加区域弹窗 */}
      {showAddRegion && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddRegion(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">添加新区域</h3>
              </div>
              
              <div className="bg-white px-6 py-4">
                <div className="text-center py-12">
                  <Plus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">添加区域功能</h3>
                  <p className="text-gray-500">区域信息录入、地图选择、覆盖范围设定等</p>
                  <p className="text-sm text-gray-400 mt-2">功能开发中...</p>
                </div>
              </div>
              
              <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                <button
                  onClick={() => setShowAddRegion(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                  保存
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RegionManagement;