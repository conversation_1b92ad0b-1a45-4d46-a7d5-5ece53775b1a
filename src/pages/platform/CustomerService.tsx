import { useState } from 'react';
import { 
  Search, 
  Filter, 
  MessageSquare, 
  Phone, 
  Mail, 
  Clock, 
  User, 
  Building2,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Send,
  Paperclip,
  MoreHorizontal,
  Star,
  TrendingUp,
  Users,
  MessageCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface Ticket {
  id: string;
  ticketNumber: string;
  title: string;
  description: string;
  category: 'order' | 'payment' | 'delivery' | 'account' | 'technical' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  customerType: 'employee' | 'merchant' | 'enterprise';
  customerName: string;
  customerContact: string;
  assignedAgent?: string;
  createdAt: string;
  updatedAt: string;
  responseTime?: number; // 响应时间（分钟）
  resolutionTime?: number; // 解决时间（小时）
}

interface ServiceStats {
  totalTickets: number;
  openTickets: number;
  resolvedToday: number;
  avgResponseTime: number;
  avgResolutionTime: number;
  customerSatisfaction: number;
}

const CustomerService = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<'all' | 'order' | 'payment' | 'delivery' | 'account' | 'technical' | 'other'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'open' | 'in_progress' | 'resolved' | 'closed'>('all');
  const [priorityFilter, setPriorityFilter] = useState<'all' | 'low' | 'medium' | 'high' | 'urgent'>('all');
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [showTicketDetail, setShowTicketDetail] = useState(false);
  const [replyMessage, setReplyMessage] = useState('');

  // 模拟客服统计数据
  const serviceStats: ServiceStats = {
    totalTickets: 1247,
    openTickets: 89,
    resolvedToday: 156,
    avgResponseTime: 12, // 分钟
    avgResolutionTime: 4.5, // 小时
    customerSatisfaction: 4.6
  };

  // 模拟工单数据
  const tickets: Ticket[] = [
    {
      id: '1',
      ticketNumber: 'CS-2024-001247',
      title: '订单配送延迟问题',
      description: '我的订单已经超过预计配送时间2小时了，请帮忙查看一下配送状态。',
      category: 'delivery',
      priority: 'high',
      status: 'open',
      customerType: 'employee',
      customerName: '张三',
      customerContact: '13800138000',
      createdAt: '2024-01-15 14:30',
      updatedAt: '2024-01-15 14:30'
    },
    {
      id: '2',
      ticketNumber: 'CS-2024-001246',
      title: '支付失败但扣款成功',
      description: '支付时显示失败，但银行卡已经扣款，请帮忙处理退款。',
      category: 'payment',
      priority: 'urgent',
      status: 'in_progress',
      customerType: 'employee',
      customerName: '李四',
      customerContact: '<EMAIL>',
      assignedAgent: '客服小王',
      createdAt: '2024-01-15 13:45',
      updatedAt: '2024-01-15 14:20',
      responseTime: 15
    },
    {
      id: '3',
      ticketNumber: 'CS-2024-001245',
      title: '商户后台无法登录',
      description: '商户后台系统无法正常登录，提示密码错误，但密码确认无误。',
      category: 'technical',
      priority: 'medium',
      status: 'resolved',
      customerType: 'merchant',
      customerName: '川味小厨',
      customerContact: '<EMAIL>',
      assignedAgent: '技术小李',
      createdAt: '2024-01-15 10:20',
      updatedAt: '2024-01-15 12:30',
      responseTime: 8,
      resolutionTime: 2.2
    },
    {
      id: '4',
      ticketNumber: 'CS-2024-001244',
      title: '企业餐费预算调整申请',
      description: '需要调整本月企业餐费预算额度，从10万调整到15万。',
      category: 'account',
      priority: 'medium',
      status: 'closed',
      customerType: 'enterprise',
      customerName: '科技有限公司',
      customerContact: '<EMAIL>',
      assignedAgent: '客服小张',
      createdAt: '2024-01-14 16:15',
      updatedAt: '2024-01-15 09:30',
      responseTime: 25,
      resolutionTime: 17.3
    },
    {
      id: '5',
      ticketNumber: 'CS-2024-001243',
      title: '订单商品质量问题投诉',
      description: '订购的商品质量有问题，希望退款并给予相应补偿。',
      category: 'order',
      priority: 'high',
      status: 'in_progress',
      customerType: 'employee',
      customerName: '王五',
      customerContact: '***********',
      assignedAgent: '客服小陈',
      createdAt: '2024-01-14 14:20',
      updatedAt: '2024-01-15 11:45',
      responseTime: 30
    }
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'order':
        return 'bg-blue-100 text-blue-800';
      case 'payment':
        return 'bg-green-100 text-green-800';
      case 'delivery':
        return 'bg-yellow-100 text-yellow-800';
      case 'account':
        return 'bg-purple-100 text-purple-800';
      case 'technical':
        return 'bg-red-100 text-red-800';
      case 'other':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryText = (category: string) => {
    switch (category) {
      case 'order':
        return '订单问题';
      case 'payment':
        return '支付问题';
      case 'delivery':
        return '配送问题';
      case 'account':
        return '账户问题';
      case 'technical':
        return '技术问题';
      case 'other':
        return '其他问题';
      default:
        return '未知';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return '紧急';
      case 'high':
        return '高';
      case 'medium':
        return '中';
      case 'low':
        return '低';
      default:
        return '未知';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open':
        return <AlertTriangle className="h-4 w-4" />;
      case 'in_progress':
        return <Clock className="h-4 w-4" />;
      case 'resolved':
        return <CheckCircle className="h-4 w-4" />;
      case 'closed':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open':
        return '待处理';
      case 'in_progress':
        return '处理中';
      case 'resolved':
        return '已解决';
      case 'closed':
        return '已关闭';
      default:
        return '未知';
    }
  };

  const getCustomerTypeIcon = (type: string) => {
    switch (type) {
      case 'employee':
        return <User className="h-4 w-4" />;
      case 'merchant':
        return <Building2 className="h-4 w-4" />;
      case 'enterprise':
        return <Users className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getCustomerTypeText = (type: string) => {
    switch (type) {
      case 'employee':
        return '员工';
      case 'merchant':
        return '商户';
      case 'enterprise':
        return '企业';
      default:
        return '未知';
    }
  };

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || ticket.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    return matchesSearch && matchesCategory && matchesStatus && matchesPriority;
  });

  const handleViewTicket = (ticket: Ticket) => {
    setSelectedTicket(ticket);
    setShowTicketDetail(true);
  };

  const handleSendReply = () => {
    if (replyMessage.trim()) {
      toast.success('回复已发送');
      setReplyMessage('');
    }
  };

  const handleAssignTicket = (ticketId: string, agent: string) => {
    toast.success(`工单已分配给${agent}`);
  };

  const handleUpdateStatus = (ticketId: string, status: string) => {
    toast.success(`工单状态已更新为${getStatusText(status)}`);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">客服系统</h1>
          <p className="text-gray-600 mt-1">管理客户服务工单和支持请求</p>
        </div>
        <button className="mt-4 sm:mt-0 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
          <MessageSquare className="h-4 w-4 mr-2" />
          创建工单
        </button>
      </div>

      {/* 客服统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总工单数</p>
              <p className="text-2xl font-bold text-gray-900">{serviceStats.totalTickets}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <MessageCircle className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+12.5%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">待处理工单</p>
              <p className="text-2xl font-bold text-gray-900">{serviceStats.openTickets}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <Clock className="h-4 w-4 text-yellow-500" />
            <span className="text-sm text-gray-500 ml-1">需要及时处理</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">今日已解决</p>
              <p className="text-2xl font-bold text-gray-900">{serviceStats.resolvedToday}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+8.3%</span>
            <span className="text-sm text-gray-500 ml-2">vs 昨日</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均响应时间</p>
              <p className="text-2xl font-bold text-gray-900">{serviceStats.avgResponseTime}分钟</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">目标: &lt;15分钟</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均解决时间</p>
              <p className="text-2xl font-bold text-gray-900">{serviceStats.avgResolutionTime}小时</p>
            </div>
            <div className="p-3 bg-indigo-100 rounded-lg">
              <Clock className="h-6 w-6 text-indigo-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <span className="text-sm text-gray-500">目标: &lt;6小时</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">客户满意度</p>
              <p className="text-2xl font-bold text-gray-900">{serviceStats.customerSatisfaction}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Star className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <Star className="h-4 w-4 text-yellow-500 fill-current" />
            <span className="text-sm text-gray-500 ml-1">5分制评分</span>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="搜索工单号、标题或客户名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">全部分类</option>
              <option value="order">订单问题</option>
              <option value="payment">支付问题</option>
              <option value="delivery">配送问题</option>
              <option value="account">账户问题</option>
              <option value="technical">技术问题</option>
              <option value="other">其他问题</option>
            </select>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">全部状态</option>
              <option value="open">待处理</option>
              <option value="in_progress">处理中</option>
              <option value="resolved">已解决</option>
              <option value="closed">已关闭</option>
            </select>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">全部优先级</option>
              <option value="urgent">紧急</option>
              <option value="high">高</option>
              <option value="medium">中</option>
              <option value="low">低</option>
            </select>
          </div>
        </div>
      </div>

      {/* 工单列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  工单信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  客户信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  分类/优先级
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  负责人
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTickets.map((ticket) => (
                <tr key={ticket.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{ticket.ticketNumber}</div>
                      <div className="text-sm text-gray-600">{ticket.title}</div>
                      <div className="text-xs text-gray-500 mt-1 max-w-xs truncate">
                        {ticket.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getCustomerTypeIcon(ticket.customerType)}
                      <div className="ml-2">
                        <div className="text-sm font-medium text-gray-900">{ticket.customerName}</div>
                        <div className="text-sm text-gray-500">{getCustomerTypeText(ticket.customerType)}</div>
                        <div className="text-xs text-gray-500">{ticket.customerContact}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(ticket.category)}`}>
                        {getCategoryText(ticket.category)}
                      </span>
                      <br />
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                        {getPriorityText(ticket.priority)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                      {getStatusIcon(ticket.status)}
                      <span className="ml-1">{getStatusText(ticket.status)}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {ticket.assignedAgent || '未分配'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{ticket.createdAt}</div>
                    <div className="text-xs text-gray-500">
                      更新：{ticket.updatedAt}
                    </div>
                    {ticket.responseTime && (
                      <div className="text-xs text-gray-500">
                        响应：{ticket.responseTime}分钟
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewTicket(ticket)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <MessageSquare className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <Phone className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 工单详情弹窗 */}
      {showTicketDetail && selectedTicket && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowTicketDetail(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">工单详情</h3>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedTicket.status)}`}>
                      {getStatusIcon(selectedTicket.status)}
                      <span className="ml-1">{getStatusText(selectedTicket.status)}</span>
                    </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(selectedTicket.priority)}`}>
                      {getPriorityText(selectedTicket.priority)}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white px-6 py-4 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">{selectedTicket.title}</h4>
                    <p className="text-sm text-gray-600 mt-2">{selectedTicket.description}</p>
                    
                    <div className="mt-4 space-y-2">
                      <div className="flex items-center text-sm">
                        <span className="font-medium text-gray-700 w-20">工单号：</span>
                        <span className="text-gray-600">{selectedTicket.ticketNumber}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="font-medium text-gray-700 w-20">分类：</span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(selectedTicket.category)}`}>
                          {getCategoryText(selectedTicket.category)}
                        </span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="font-medium text-gray-700 w-20">负责人：</span>
                        <span className="text-gray-600">{selectedTicket.assignedAgent || '未分配'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-3">客户信息</h5>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm">
                        {getCustomerTypeIcon(selectedTicket.customerType)}
                        <span className="ml-2 font-medium text-gray-700">{selectedTicket.customerName}</span>
                        <span className="ml-2 text-gray-500">({getCustomerTypeText(selectedTicket.customerType)})</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Phone className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">{selectedTicket.customerContact}</span>
                      </div>
                    </div>

                    <h5 className="text-sm font-medium text-gray-900 mb-3 mt-6">时间信息</h5>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">创建时间：</span>
                        <span className="font-medium">{selectedTicket.createdAt}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">更新时间：</span>
                        <span className="font-medium">{selectedTicket.updatedAt}</span>
                      </div>
                      {selectedTicket.responseTime && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">响应时间：</span>
                          <span className="font-medium">{selectedTicket.responseTime}分钟</span>
                        </div>
                      )}
                      {selectedTicket.resolutionTime && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">解决时间：</span>
                          <span className="font-medium">{selectedTicket.resolutionTime}小时</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 回复区域 */}
                <div className="border-t border-gray-200 pt-6">
                  <h5 className="text-sm font-medium text-gray-900 mb-3">回复客户</h5>
                  <div className="space-y-4">
                    <textarea
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                      placeholder="输入回复内容..."
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <div className="flex items-center justify-between">
                      <button className="flex items-center text-gray-600 hover:text-gray-900">
                        <Paperclip className="h-4 w-4 mr-1" />
                        添加附件
                      </button>
                      <div className="flex space-x-3">
                        <select className="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                          <option>更新状态</option>
                          <option value="in_progress">处理中</option>
                          <option value="resolved">已解决</option>
                          <option value="closed">关闭工单</option>
                        </select>
                        <button
                          onClick={handleSendReply}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                        >
                          <Send className="h-4 w-4 mr-2" />
                          发送回复
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                <button
                  onClick={() => setShowTicketDetail(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerService;