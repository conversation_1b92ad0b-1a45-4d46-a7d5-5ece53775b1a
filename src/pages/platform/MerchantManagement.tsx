import { useState } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Eye, 
  Ban, 
  CheckCircle, 
  XCircle, 
  Clock,
  Building2,
  Phone,
  Mail,
  MapPin,
  Star,
  DollarSign,
  Package,
  TrendingUp,
  MoreHorizontal
} from 'lucide-react';
import { toast } from 'sonner';

interface Merchant {
  id: string;
  name: string;
  logo: string;
  category: string;
  address: string;
  phone: string;
  email: string;
  contactPerson: string;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  rating: number;
  reviewCount: number;
  totalOrders: number;
  monthlyRevenue: number;
  joinDate: string;
  lastActive: string;
  commission: number;
}

const MerchantManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'pending' | 'suspended'>('all');
  const [selectedMerchant, setSelectedMerchant] = useState<Merchant | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // 模拟数据
  const merchants: Merchant[] = [
    {
      id: '1',
      name: '川味小厨',
      logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=chinese%20restaurant%20logo%20simple%20clean&image_size=square',
      category: '川菜',
      address: '北京市朝阳区建国路88号',
      phone: '010-12345678',
      email: '<EMAIL>',
      contactPerson: '张师傅',
      status: 'active',
      rating: 4.8,
      reviewCount: 1234,
      totalOrders: 5678,
      monthlyRevenue: 125000,
      joinDate: '2023-01-15',
      lastActive: '2024-01-15 14:30',
      commission: 8.5
    },
    {
      id: '2',
      name: '美味餐厅',
      logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20restaurant%20logo%20elegant&image_size=square',
      category: '粤菜',
      address: '上海市浦东新区陆家嘴金融区',
      phone: '021-87654321',
      email: '<EMAIL>',
      contactPerson: '李经理',
      status: 'pending',
      rating: 4.6,
      reviewCount: 892,
      totalOrders: 3456,
      monthlyRevenue: 98000,
      joinDate: '2023-03-20',
      lastActive: '2024-01-14 16:45',
      commission: 9.0
    },
    {
      id: '3',
      name: '快乐汉堡',
      logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=burger%20restaurant%20logo%20colorful&image_size=square',
      category: '西式快餐',
      address: '广州市天河区珠江新城',
      phone: '020-11223344',
      email: '<EMAIL>',
      contactPerson: '王店长',
      status: 'suspended',
      rating: 4.2,
      reviewCount: 567,
      totalOrders: 2345,
      monthlyRevenue: 67000,
      joinDate: '2023-06-10',
      lastActive: '2024-01-10 09:20',
      commission: 7.5
    },
    {
      id: '4',
      name: '日式料理',
      logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=japanese%20restaurant%20logo%20minimalist&image_size=square',
      category: '日料',
      address: '深圳市南山区科技园',
      phone: '0755-99887766',
      email: '<EMAIL>',
      contactPerson: '田中先生',
      status: 'active',
      rating: 4.9,
      reviewCount: 2156,
      totalOrders: 8901,
      monthlyRevenue: 189000,
      joinDate: '2022-11-05',
      lastActive: '2024-01-15 18:15',
      commission: 8.0
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'suspended':
        return <Ban className="h-4 w-4" />;
      case 'inactive':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '正常营业';
      case 'pending':
        return '待审核';
      case 'suspended':
        return '已暂停';
      case 'inactive':
        return '已停业';
      default:
        return '未知';
    }
  };

  const filteredMerchants = merchants.filter(merchant => {
    const matchesSearch = merchant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         merchant.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         merchant.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || merchant.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleStatusChange = (merchantId: string, newStatus: string) => {
    toast.success(`商户状态已更新为：${getStatusText(newStatus)}`);
  };

  const handleViewDetails = (merchant: Merchant) => {
    setSelectedMerchant(merchant);
    setShowDetails(true);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">商户管理</h1>
          <p className="text-gray-600 mt-1">管理平台所有商户信息</p>
        </div>
        <button className="mt-4 sm:mt-0 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
          <Plus className="h-4 w-4 mr-2" />
          添加商户
        </button>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="搜索商户名称、分类或联系人..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">全部状态</option>
              <option value="active">正常营业</option>
              <option value="pending">待审核</option>
              <option value="suspended">已暂停</option>
              <option value="inactive">已停业</option>
            </select>
            <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="h-4 w-4 mr-2" />
              更多筛选
            </button>
          </div>
        </div>
      </div>

      {/* 商户列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  商户信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  联系方式
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  经营数据
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  佣金比例
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMerchants.map((merchant) => (
                <tr key={merchant.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <img
                        src={merchant.logo}
                        alt={merchant.name}
                        className="h-10 w-10 rounded-lg object-cover"
                      />
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{merchant.name}</div>
                        <div className="text-sm text-gray-500">{merchant.category}</div>
                        <div className="flex items-center mt-1">
                          <Star className="h-3 w-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-500 ml-1">
                            {merchant.rating} ({merchant.reviewCount}评价)
                          </span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{merchant.contactPerson}</div>
                    <div className="text-sm text-gray-500 flex items-center mt-1">
                      <Phone className="h-3 w-3 mr-1" />
                      {merchant.phone}
                    </div>
                    <div className="text-sm text-gray-500 flex items-center mt-1">
                      <Mail className="h-3 w-3 mr-1" />
                      {merchant.email}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(merchant.status)}`}>
                      {getStatusIcon(merchant.status)}
                      <span className="ml-1">{getStatusText(merchant.status)}</span>
                    </span>
                    <div className="text-xs text-gray-500 mt-1">
                      最后活跃：{merchant.lastActive}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 flex items-center">
                      <DollarSign className="h-3 w-3 mr-1" />
                      ¥{merchant.monthlyRevenue.toLocaleString()}/月
                    </div>
                    <div className="text-sm text-gray-500 flex items-center mt-1">
                      <Package className="h-3 w-3 mr-1" />
                      {merchant.totalOrders}订单
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{merchant.commission}%</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewDetails(merchant)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 商户详情弹窗 */}
      {showDetails && selectedMerchant && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowDetails(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">商户详情</h3>
              </div>
              
              <div className="bg-white px-6 py-4 space-y-6">
                <div className="flex items-center space-x-4">
                  <img
                    src={selectedMerchant.logo}
                    alt={selectedMerchant.name}
                    className="h-16 w-16 rounded-lg object-cover"
                  />
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">{selectedMerchant.name}</h4>
                    <p className="text-gray-600">{selectedMerchant.category}</p>
                    <div className="flex items-center mt-2">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 ml-1">
                        {selectedMerchant.rating} ({selectedMerchant.reviewCount}评价)
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-3">基本信息</h5>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm">
                        <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">{selectedMerchant.address}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Phone className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">{selectedMerchant.phone}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Mail className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">{selectedMerchant.email}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-3">经营数据</h5>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">月收入：</span>
                        <span className="font-medium">¥{selectedMerchant.monthlyRevenue.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">总订单：</span>
                        <span className="font-medium">{selectedMerchant.totalOrders}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">佣金比例：</span>
                        <span className="font-medium">{selectedMerchant.commission}%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">入驻时间：</span>
                        <span className="font-medium">{selectedMerchant.joinDate}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
                <button
                  onClick={() => setShowDetails(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  关闭
                </button>
                <button className="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700">
                  编辑商户
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MerchantManagement;