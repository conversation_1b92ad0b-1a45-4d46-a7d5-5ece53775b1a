import { useState } from 'react';
import { 
  Search, 
  Filter, 
  Download, 
  Upload, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  CreditCard,
  Building2,
  Users,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Eye,
  MoreHorizontal
} from 'lucide-react';
import { toast } from 'sonner';

interface FinancialRecord {
  id: string;
  type: 'merchant_settlement' | 'enterprise_payment' | 'platform_commission' | 'refund';
  merchantName?: string;
  enterpriseName?: string;
  amount: number;
  commission: number;
  netAmount: number;
  status: 'pending' | 'completed' | 'failed' | 'disputed';
  date: string;
  settlementDate?: string;
  orderCount: number;
  description: string;
}

interface FinancialSummary {
  totalRevenue: number;
  totalCommission: number;
  merchantSettlement: number;
  enterprisePayment: number;
  pendingAmount: number;
  disputedAmount: number;
}

const FinancialManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'merchant_settlement' | 'enterprise_payment' | 'platform_commission' | 'refund'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'completed' | 'failed' | 'disputed'>('all');
  const [dateRange, setDateRange] = useState<'today' | 'week' | 'month' | 'quarter'>('month');

  // 模拟财务汇总数据
  const financialSummary: FinancialSummary = {
    totalRevenue: 2847392,
    totalCommission: 227791,
    merchantSettlement: 2619601,
    enterprisePayment: 1456789,
    pendingAmount: 156789,
    disputedAmount: 23456
  };

  // 模拟财务记录数据
  const financialRecords: FinancialRecord[] = [
    {
      id: '1',
      type: 'merchant_settlement',
      merchantName: '川味小厨',
      amount: 125000,
      commission: 10625,
      netAmount: 114375,
      status: 'completed',
      date: '2024-01-15',
      settlementDate: '2024-01-16',
      orderCount: 456,
      description: '2024年1月上半月结算'
    },
    {
      id: '2',
      type: 'enterprise_payment',
      enterpriseName: '科技有限公司',
      amount: 128500,
      commission: 0,
      netAmount: 128500,
      status: 'pending',
      date: '2024-01-15',
      orderCount: 234,
      description: '企业餐费充值'
    },
    {
      id: '3',
      type: 'platform_commission',
      merchantName: '美味餐厅',
      amount: 98000,
      commission: 8820,
      netAmount: 8820,
      status: 'completed',
      date: '2024-01-14',
      settlementDate: '2024-01-15',
      orderCount: 189,
      description: '平台佣金收入'
    },
    {
      id: '4',
      type: 'refund',
      merchantName: '快乐汉堡',
      amount: 2350,
      commission: -188,
      netAmount: 2162,
      status: 'disputed',
      date: '2024-01-13',
      orderCount: 12,
      description: '订单退款处理'
    },
    {
      id: '5',
      type: 'merchant_settlement',
      merchantName: '日式料理',
      amount: 189000,
      commission: 15120,
      netAmount: 173880,
      status: 'failed',
      date: '2024-01-12',
      orderCount: 567,
      description: '结算失败，银行账户异常'
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'merchant_settlement':
        return 'bg-blue-100 text-blue-800';
      case 'enterprise_payment':
        return 'bg-green-100 text-green-800';
      case 'platform_commission':
        return 'bg-purple-100 text-purple-800';
      case 'refund':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'merchant_settlement':
        return '商户结算';
      case 'enterprise_payment':
        return '企业付款';
      case 'platform_commission':
        return '平台佣金';
      case 'refund':
        return '退款处理';
      default:
        return '未知';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'disputed':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      case 'disputed':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'pending':
        return '处理中';
      case 'failed':
        return '失败';
      case 'disputed':
        return '争议中';
      default:
        return '未知';
    }
  };

  const filteredRecords = financialRecords.filter(record => {
    const matchesSearch = (record.merchantName?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
                         (record.enterpriseName?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
                         record.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || record.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter;
    return matchesSearch && matchesType && matchesStatus;
  });

  const handleExportReport = () => {
    toast.success('财务报表导出成功');
  };

  const handleBatchSettle = () => {
    toast.success('批量结算操作已提交');
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">财务对账</h1>
          <p className="text-gray-600 mt-1">管理平台财务结算和对账</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button 
            onClick={handleExportReport}
            className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            导出报表
          </button>
          <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center">
            <Upload className="h-4 w-4 mr-2" />
            导入对账单
          </button>
          <button 
            onClick={handleBatchSettle}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <CreditCard className="h-4 w-4 mr-2" />
            批量结算
          </button>
        </div>
      </div>

      {/* 财务汇总卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总收入</p>
              <p className="text-2xl font-bold text-gray-900">¥{financialSummary.totalRevenue.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+12.5%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平台佣金</p>
              <p className="text-2xl font-bold text-gray-900">¥{financialSummary.totalCommission.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <CreditCard className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+8.3%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">商户结算</p>
              <p className="text-2xl font-bold text-gray-900">¥{financialSummary.merchantSettlement.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+15.2%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">企业付款</p>
              <p className="text-2xl font-bold text-gray-900">¥{financialSummary.enterprisePayment.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Users className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-600 ml-1">+6.7%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">待处理金额</p>
              <p className="text-2xl font-bold text-gray-900">¥{financialSummary.pendingAmount.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingDown className="h-4 w-4 text-red-500" />
            <span className="text-sm font-medium text-red-600 ml-1">-3.2%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">争议金额</p>
              <p className="text-2xl font-bold text-gray-900">¥{financialSummary.disputedAmount.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div className="flex items-center mt-4">
            <TrendingDown className="h-4 w-4 text-red-500" />
            <span className="text-sm font-medium text-red-600 ml-1">-12.1%</span>
            <span className="text-sm text-gray-500 ml-2">vs 上月</span>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="搜索商户、企业或描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">全部类型</option>
              <option value="merchant_settlement">商户结算</option>
              <option value="enterprise_payment">企业付款</option>
              <option value="platform_commission">平台佣金</option>
              <option value="refund">退款处理</option>
            </select>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">全部状态</option>
              <option value="completed">已完成</option>
              <option value="pending">处理中</option>
              <option value="failed">失败</option>
              <option value="disputed">争议中</option>
            </select>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="today">今日</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
              <option value="quarter">本季度</option>
            </select>
          </div>
        </div>
      </div>

      {/* 财务记录列表 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  交易信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  金额详情
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  日期
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRecords.map((record) => (
                <tr key={record.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {record.merchantName || record.enterpriseName || '平台'}
                      </div>
                      <div className="text-sm text-gray-500">{record.description}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {record.orderCount}笔订单
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(record.type)}`}>
                      {getTypeText(record.type)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div>总额：¥{record.amount.toLocaleString()}</div>
                      {record.commission !== 0 && (
                        <div className="text-xs text-gray-500">
                          佣金：¥{record.commission.toLocaleString()}
                        </div>
                      )}
                      <div className="text-xs font-medium text-gray-900">
                        净额：¥{record.netAmount.toLocaleString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                      {getStatusIcon(record.status)}
                      <span className="ml-1">{getStatusText(record.status)}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{record.date}</div>
                    {record.settlementDate && (
                      <div className="text-xs text-gray-500">
                        结算：{record.settlementDate}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="text-blue-600 hover:text-blue-900">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <FileText className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default FinancialManagement;