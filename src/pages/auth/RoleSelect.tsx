import { useNavigate } from 'react-router-dom';
import { useAppStore } from '../../store';
import { Smartphone, Store, Building2, Settings } from 'lucide-react';

const RoleSelect = () => {
  const navigate = useNavigate();
  const { setCurrentRole } = useAppStore();
  

  


  const roles = [
    {
      id: 'employee',
      name: '员工点餐',
      description: '企业员工点餐小程序',
      icon: Smartphone,
      color: 'bg-gradient-to-br from-orange-500 to-red-500',
      path: '/employee'
    },
    {
      id: 'merchant',
      name: '商户管理',
      description: '商户管理后台系统',
      icon: Store,
      color: 'bg-gradient-to-br from-blue-500 to-indigo-500',
      path: '/merchant'
    },
    {
      id: 'enterprise_admin',
      name: '企业管理',
      description: '企业客户管理后台',
      icon: Building2,
      color: 'bg-gradient-to-br from-green-500 to-emerald-500',
      path: '/enterprise'
    },
    {
      id: 'platform_admin',
      name: '平台管理',
      description: '平台运营管理后台',
      icon: Settings,
      color: 'bg-gradient-to-br from-purple-500 to-pink-500',
      path: '/platform'
    }
  ];

  const handleRoleSelect = (role: any) => {
    setCurrentRole(role.id);
    navigate(role.path);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">

      <div className="max-w-4xl w-full">
        {/* 头部 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            企业餐饮服务平台
          </h1>
          <p className="text-xl text-gray-600">
            请选择您要进入的系统
          </p>
        </div>

        {/* 角色选择卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {roles.map((role) => {
            const IconComponent = role.icon;
            return (
              <div
                key={role.id}
                onClick={() => handleRoleSelect(role)}
                className="group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
              >
                <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 h-full flex flex-col items-center text-center">
                  {/* 图标 */}
                  <div className={`${role.color} p-4 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  
                  {/* 标题 */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {role.name}
                  </h3>
                  
                  {/* 描述 */}
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {role.description}
                  </p>
                  
                  {/* 进入按钮 */}
                  <div className="mt-6 w-full">
                    <div className="bg-gray-100 group-hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors duration-200 text-sm font-medium">点击进入</div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 底部信息 */}
        <div className="text-center mt-12">
          <p className="text-gray-500 text-sm">
            智能餐饮解决方案 · 覆盖点餐、配送、取餐全流程
          </p>
        </div>
      </div>
    </div>
  );
};

export default RoleSelect;