import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  User, 
  Wallet, 
  CreditCard, 
  MapPin, 
  Bell, 
  Settings, 
  HelpCircle, 
  LogOut,
  ChevronRight,
  Gift,
  Star,
  Clock,
  Shield,
  Phone,
  Mail,
  Edit3
} from 'lucide-react';
import { useUserStore } from '../../store';
import { toast } from 'sonner';

const Profile = () => {
  const navigate = useNavigate();
  const { user, logout } = useUserStore();
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  
  // 模拟用户数据
  const mockUserData = {
    name: '张三',
    phone: '138****5678',
    email: '<EMAIL>',
    avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20business%20person%20avatar%20portrait%20friendly%20smile&image_size=square',
    department: '技术部',
    position: '高级工程师',
    employeeId: 'EMP001',
    joinDate: '2022-03-15',
    mealAllowanceBalance: 125.50,
    walletBalance: 256.80,
    totalOrders: 156,
    favoriteCount: 23,
    reviewCount: 45,
    couponCount: 8
  };

  const menuSections = [
    {
      title: '账户管理',
      items: [
        {
          icon: <Wallet className="w-5 h-5 text-green-500" />,
          label: '我的钱包',
          value: `¥${mockUserData.walletBalance.toFixed(2)}`,
          onClick: () => navigate('/employee/wallet')
        },
        {
          icon: <CreditCard className="w-5 h-5 text-orange-500" />,
          label: '餐标余额',
          value: `¥${mockUserData.mealAllowanceBalance.toFixed(2)}`,
          onClick: () => navigate('/employee/meal-allowance')
        },
        {
          icon: <Gift className="w-5 h-5 text-purple-500" />,
          label: '我的优惠券',
          value: `${mockUserData.couponCount}张`,
          onClick: () => navigate('/employee/coupons')
        },
        {
          icon: <MapPin className="w-5 h-5 text-blue-500" />,
          label: '收货地址',
          onClick: () => navigate('/employee/addresses')
        }
      ]
    },
    {
      title: '我的活动',
      items: [
        {
          icon: <Star className="w-5 h-5 text-yellow-500" />,
          label: '我的收藏',
          value: `${mockUserData.favoriteCount}个`,
          onClick: () => navigate('/employee/favorites')
        },
        {
          icon: <Clock className="w-5 h-5 text-indigo-500" />,
          label: '历史订单',
          value: `${mockUserData.totalOrders}单`,
          onClick: () => navigate('/employee/orders')
        }
      ]
    },
    {
      title: '设置与帮助',
      items: [
        {
          icon: <Bell className="w-5 h-5 text-red-500" />,
          label: '消息通知',
          onClick: () => navigate('/employee/notifications')
        },
        {
          icon: <Settings className="w-5 h-5 text-gray-500" />,
          label: '设置',
          onClick: () => navigate('/employee/settings')
        },
        {
          icon: <Shield className="w-5 h-5 text-green-600" />,
          label: '隐私政策',
          onClick: () => toast.info('隐私政策页面')
        },
        {
          icon: <HelpCircle className="w-5 h-5 text-blue-600" />,
          label: '帮助与反馈',
          onClick: () => toast.info('帮助与反馈页面')
        }
      ]
    }
  ];

  const handleLogout = () => {
    logout();
    toast.success('已退出登录');
    navigate('/login');
    setShowLogoutModal(false);
  };

  const handleEditProfile = () => {
    toast.info('编辑个人信息功能开发中');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部个人信息 */}
      <div className="bg-gradient-to-br from-orange-500 to-red-500 px-4 pt-12 pb-8">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <img
              src={mockUserData.avatar}
              alt="头像"
              className="w-20 h-20 rounded-full border-4 border-white shadow-lg"
            />
            <button
              onClick={handleEditProfile}
              className="absolute -bottom-1 -right-1 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg hover:bg-gray-50 transition-colors"
            >
              <Edit3 className="w-4 h-4 text-gray-600" />
            </button>
          </div>
          
          <div className="flex-1 text-white">
            <h1 className="text-xl font-bold mb-1">{mockUserData.name}</h1>
            <div className="space-y-1 text-sm text-white/90">
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4" />
                <span>{mockUserData.department} · {mockUserData.position}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span>{mockUserData.phone}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4" />
                <span>{mockUserData.email}</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* 统计信息 */}
        <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-white/20">
          <div className="text-center text-white">
            <div className="text-2xl font-bold">{mockUserData.totalOrders}</div>
            <div className="text-sm text-white/80">总订单</div>
          </div>
          <div className="text-center text-white">
            <div className="text-2xl font-bold">{mockUserData.favoriteCount}</div>
            <div className="text-sm text-white/80">收藏</div>
          </div>
          <div className="text-center text-white">
            <div className="text-2xl font-bold">{mockUserData.reviewCount}</div>
            <div className="text-sm text-white/80">评价</div>
          </div>
        </div>
      </div>

      {/* 菜单列表 */}
      <div className="px-4 py-6 space-y-6">
        {menuSections.map((section, sectionIndex) => (
          <div key={sectionIndex} className="bg-white rounded-2xl shadow-sm overflow-hidden">
            <div className="px-4 py-3 border-b border-gray-100">
              <h2 className="font-semibold text-gray-900">{section.title}</h2>
            </div>
            <div className="divide-y divide-gray-100">
              {section.items.map((item, itemIndex) => (
                <button
                  key={itemIndex}
                  onClick={item.onClick}
                  className="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    {item.icon}
                    <span className="font-medium text-gray-900">{item.label}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {item.value && (
                      <span className="text-sm text-gray-500">{item.value}</span>
                    )}
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  </div>
                </button>
              ))}
            </div>
          </div>
        ))}

        {/* 退出登录 */}
        <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
          <button
            onClick={() => setShowLogoutModal(true)}
            className="w-full px-4 py-4 flex items-center justify-center space-x-2 text-red-600 hover:bg-red-50 transition-colors"
          >
            <LogOut className="w-5 h-5" />
            <span className="font-medium">退出登录</span>
          </button>
        </div>
      </div>

      {/* 版本信息 */}
      <div className="text-center text-gray-500 text-sm pb-8">
        <div>员工号：{mockUserData.employeeId}</div>
        <div>入职时间：{mockUserData.joinDate}</div>
        <div className="mt-2">企业餐饮服务平台 v1.0.0</div>
      </div>

      {/* 退出登录确认弹窗 */}
      {showLogoutModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl w-full max-w-sm">
            <div className="p-6 text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <LogOut className="w-8 h-8 text-red-500" />
              </div>
              <h2 className="text-lg font-semibold text-gray-900 mb-2">确认退出</h2>
              <p className="text-gray-600 mb-6">您确定要退出当前账户吗？</p>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowLogoutModal(false)}
                  className="flex-1 py-3 text-gray-600 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleLogout}
                  className="flex-1 py-3 text-white bg-red-500 rounded-xl hover:bg-red-600 transition-colors"
                >
                  确认退出
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Profile;