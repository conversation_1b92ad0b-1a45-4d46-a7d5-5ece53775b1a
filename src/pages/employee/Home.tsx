import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Star, Plus, Clock, MapPin } from 'lucide-react';
import { useCartStore } from '../../store';
import { Product, Category } from '../../types';
import { toast } from 'sonner';

const EmployeeHome = () => {
  const navigate = useNavigate();
  const { addItem } = useCartStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [banners, setBanners] = useState<string[]>([]);
  const [currentBanner, setCurrentBanner] = useState(0);

  // 模拟数据
  useEffect(() => {
    // 模拟轮播图
    setBanners([
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=delicious%20food%20banner%20restaurant%20promotion&image_size=landscape_16_9',
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=healthy%20meal%20delivery%20service%20banner&image_size=landscape_16_9',
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=fresh%20ingredients%20cooking%20banner&image_size=landscape_16_9'
    ]);

    // 模拟分类数据
    setCategories([
      { id: 'all', name: '全部', icon: '🍽️', parentId: undefined, sort: 0 },
      { id: 'hot', name: '热销', icon: '🔥', parentId: undefined, sort: 1 },
      { id: 'main', name: '主食', icon: '🍚', parentId: undefined, sort: 2 },
      { id: 'soup', name: '汤品', icon: '🍲', parentId: undefined, sort: 3 },
      { id: 'vegetable', name: '蔬菜', icon: '🥬', parentId: undefined, sort: 4 },
      { id: 'drink', name: '饮品', icon: '🥤', parentId: undefined, sort: 5 },
      { id: 'snack', name: '小食', icon: '🍿', parentId: undefined, sort: 6 },
      { id: 'dessert', name: '甜品', icon: '🍰', parentId: undefined, sort: 7 }
    ]);

    // 模拟商品数据
    setProducts([
      {
        id: '1',
        name: '红烧肉套餐',
        description: '精选五花肉，配白米饭、时令蔬菜、紫菜蛋花汤',
        price: 28.8,
        originalPrice: 32.8,
        image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=braised%20pork%20rice%20set%20meal%20chinese%20food&image_size=square',
        categoryId: 'main',
        merchantId: 'merchant_1',
        stock: 50,
        sales: 128,
        rating: 4.8,
        reviewCount: 45,
        tags: ['热销', '下饭'],
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: '宫保鸡丁',
        description: '经典川菜，鸡肉嫩滑，花生香脆，配米饭',
        price: 26.8,
        image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=kung%20pao%20chicken%20chinese%20dish%20with%20peanuts&image_size=square',
        categoryId: 'main',
        merchantId: 'merchant_1',
        stock: 30,
        sales: 89,
        rating: 4.6,
        reviewCount: 32,
        tags: ['川菜', '经典'],
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '3',
        name: '番茄鸡蛋汤',
        description: '新鲜番茄配嫩滑鸡蛋，酸甜开胃',
        price: 8.8,
        image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=tomato%20egg%20soup%20chinese%20style&image_size=square',
        categoryId: 'soup',
        merchantId: 'merchant_1',
        stock: 100,
        sales: 156,
        rating: 4.7,
        reviewCount: 67,
        tags: ['清淡', '开胃'],
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '4',
        name: '时令蔬菜',
        description: '当季新鲜蔬菜，清炒或蒜蓉炒制',
        price: 12.8,
        image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=fresh%20seasonal%20vegetables%20stir%20fried&image_size=square',
        categoryId: 'vegetable',
        merchantId: 'merchant_1',
        stock: 80,
        sales: 78,
        rating: 4.5,
        reviewCount: 28,
        tags: ['健康', '时令'],
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '5',
        name: '柠檬蜂蜜茶',
        description: '新鲜柠檬配天然蜂蜜，清香解腻',
        price: 15.8,
        image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=lemon%20honey%20tea%20refreshing%20drink&image_size=square',
        categoryId: 'drink',
        merchantId: 'merchant_1',
        stock: 60,
        sales: 92,
        rating: 4.9,
        reviewCount: 38,
        tags: ['清香', '解腻'],
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '6',
        name: '芒果布丁',
        description: '香甜芒果制作，口感顺滑，颜值很高',
        price: 18.8,
        image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=mango%20pudding%20dessert%20smooth%20texture&image_size=square',
        categoryId: 'dessert',
        merchantId: 'merchant_1',
        stock: 40,
        sales: 65,
        rating: 4.8,
        reviewCount: 25,
        tags: ['香甜', '颜值'],
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      }
    ]);
  }, []);

  // 轮播图自动切换
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentBanner((prev) => (prev + 1) % banners.length);
    }, 3000);
    return () => clearInterval(timer);
  }, [banners.length]);

  // 过滤商品
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || 
                           product.categoryId === selectedCategory ||
                           (selectedCategory === 'hot' && product.sales > 100);
    return matchesSearch && matchesCategory;
  });

  const handleAddToCart = (product: Product) => {
    addItem(product, 1);
    toast.success(`${product.name} 已加入购物车`);
  };

  const handleProductClick = (productId: string) => {
    navigate(`/employee/product/${productId}`);
  };

  return (
    <div className="space-y-4">
      {/* 搜索栏 */}
      <div className="bg-white px-4 py-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索美食..."
            className="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-full border-0 focus:ring-2 focus:ring-orange-500 focus:bg-white transition-all"
          />
        </div>
      </div>

      {/* 轮播图 */}
      <div className="px-4">
        <div className="relative h-40 rounded-2xl overflow-hidden">
          {banners.map((banner, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-500 ${
                index === currentBanner ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <img
                src={banner}
                alt={`Banner ${index + 1}`}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            </div>
          ))}
          
          {/* 轮播指示器 */}
          <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {banners.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentBanner(index)}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentBanner ? 'bg-white' : 'bg-white bg-opacity-50'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* 餐标余额提示 */}
      <div className="mx-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-4 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold mb-1">今日餐标余额</h3>
            <p className="text-orange-100 text-sm">午餐时段 11:30-13:30</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">¥25.00</div>
            <div className="text-orange-100 text-sm flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              截单时间：11:00
            </div>
          </div>
        </div>
      </div>

      {/* 分类导航 */}
      <div className="bg-white py-4">
        <div className="px-4 mb-3">
          <h2 className="text-lg font-semibold text-gray-900">商品分类</h2>
        </div>
        <div className="flex overflow-x-auto px-4 space-x-4 scrollbar-hide">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex-shrink-0 flex flex-col items-center space-y-2 p-3 rounded-xl transition-all ${
                selectedCategory === category.id
                  ? 'bg-orange-100 text-orange-600'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <div className={`text-2xl w-12 h-12 rounded-xl flex items-center justify-center ${
                selectedCategory === category.id
                  ? 'bg-orange-200'
                  : 'bg-gray-100'
              }`}>
                {category.icon}
              </div>
              <span className="text-sm font-medium whitespace-nowrap">
                {category.name}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* 商品列表 */}
      <div className="bg-white">
        <div className="px-4 py-3 border-b border-gray-100">
          <h2 className="text-lg font-semibold text-gray-900">
            {selectedCategory === 'all' ? '全部商品' : 
             selectedCategory === 'hot' ? '热销商品' :
             categories.find(c => c.id === selectedCategory)?.name || '商品列表'}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            共 {filteredProducts.length} 件商品
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-4 p-4">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-2xl border border-gray-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow"
            >
              {/* 商品图片 */}
              <div 
                className="relative cursor-pointer"
                onClick={() => handleProductClick(product.id)}
              >
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-32 object-cover"
                />
                {product.originalPrice && (
                  <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    特价
                  </div>
                )}
                {product.sales > 100 && (
                  <div className="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                    热销
                  </div>
                )}
              </div>
              
              {/* 商品信息 */}
              <div className="p-3">
                <h3 
                  className="font-semibold text-gray-900 mb-1 cursor-pointer hover:text-orange-600 transition-colors"
                  onClick={() => handleProductClick(product.id)}
                >
                  {product.name}
                </h3>
                <p className="text-xs text-gray-500 mb-2 line-clamp-2">
                  {product.description}
                </p>
                
                {/* 评分和销量 */}
                <div className="flex items-center space-x-2 mb-2">
                  <div className="flex items-center space-x-1">
                    <Star className="w-3 h-3 text-yellow-400 fill-current" />
                    <span className="text-xs text-gray-600">{product.rating}</span>
                  </div>
                  <span className="text-xs text-gray-400">·</span>
                  <span className="text-xs text-gray-600">销量{product.sales}</span>
                </div>
                
                {/* 价格和加购按钮 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <span className="text-lg font-bold text-red-500">
                      ¥{product.price}
                    </span>
                    {product.originalPrice && (
                      <span className="text-xs text-gray-400 line-through">
                        ¥{product.originalPrice}
                      </span>
                    )}
                  </div>
                  <button
                    onClick={() => handleAddToCart(product)}
                    className="bg-orange-500 hover:bg-orange-600 text-white p-2 rounded-full transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-2">🍽️</div>
            <p className="text-gray-500">暂无相关商品</p>
          </div>
        )}
      </div>

      {/* 配送信息 */}
      <div className="mx-4 bg-blue-50 rounded-2xl p-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
            <MapPin className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-blue-900">配送到智能餐柜</h3>
            <p className="text-blue-600 text-sm">预计30分钟送达 · 1号楼1层餐柜</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeHome;