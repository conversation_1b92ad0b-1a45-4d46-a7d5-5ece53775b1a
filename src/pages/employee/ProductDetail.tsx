import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Star, Plus, Minus, ShoppingCart, Heart, Share2 } from 'lucide-react';
import { useCartStore } from '../../store';
import { Product, Review } from '../../types';
import { toast } from 'sonner';

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addItem } = useCartStore();
  const [product, setProduct] = useState<Product | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [selectedSpecs, setSelectedSpecs] = useState<{ [specId: string]: string }>({});
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);

  useEffect(() => {
    if (id) {
      // 模拟获取商品详情
      const mockProduct: Product = {
        id: id,
        name: '红烧肉套餐',
        description: '精选优质五花肉，经过传统红烧工艺精心制作，肥瘦相间，入口即化。配有香喷喷的白米饭、时令新鲜蔬菜和营养丰富的紫菜蛋花汤，营养搭配均衡，是您午餐的不二选择。',
        price: 28.8,
        originalPrice: 32.8,
        image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=braised%20pork%20rice%20set%20meal%20chinese%20food&image_size=square',
        images: [
          'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=braised%20pork%20rice%20set%20meal%20chinese%20food&image_size=square',
          'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=braised%20pork%20belly%20close%20up%20delicious&image_size=square',
          'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=white%20rice%20and%20vegetables%20side%20dish&image_size=square'
        ],
        categoryId: 'main',
        merchantId: 'merchant_1',
        stock: 50,
        sales: 128,
        rating: 4.8,
        reviewCount: 45,
        tags: ['热销', '下饭', '经典'],
        specifications: [
          {
            id: 'spice_level',
            name: '辣度',
            options: [
              { id: 'mild', name: '微辣', price: 0, stock: 50 },
              { id: 'medium', name: '中辣', price: 0, stock: 30 },
              { id: 'hot', name: '重辣', price: 2, stock: 20 }
            ]
          },
          {
            id: 'rice_type',
            name: '米饭',
            options: [
              { id: 'normal', name: '普通米饭', price: 0, stock: 100 },
              { id: 'brown', name: '糙米饭', price: 3, stock: 50 },
              { id: 'extra', name: '加量米饭', price: 2, stock: 80 }
            ]
          }
        ],
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      };
      setProduct(mockProduct);

      // 模拟评价数据
      const mockReviews: Review[] = [
        {
          id: '1',
          orderId: 'order_1',
          userId: 'user_1',
          userName: '张三',
          userAvatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20avatar%20portrait&image_size=square',
          productId: id,
          rating: 5,
          content: '红烧肉做得很棒，肥瘦相间，入口即化，配菜也很新鲜，性价比很高！',
          images: ['https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=delicious%20braised%20pork%20meal%20photo&image_size=square'],
          merchantReply: '感谢您的好评，我们会继续保持品质！',
          createdAt: '2024-01-15T12:30:00Z'
        },
        {
          id: '2',
          orderId: 'order_2',
          userId: 'user_2',
          userName: '李四',
          userAvatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20avatar%20portrait%20woman&image_size=square',
          productId: id,
          rating: 4,
          content: '味道不错，就是分量稍微少了一点，希望能再多一些。',
          createdAt: '2024-01-14T11:45:00Z'
        },
        {
          id: '3',
          orderId: 'order_3',
          userId: 'user_3',
          userName: '王五',
          productId: id,
          rating: 5,
          content: '经常点这个套餐，每次都很满意，红烧肉软糯香甜，配菜搭配合理。',
          createdAt: '2024-01-13T13:20:00Z'
        }
      ];
      setReviews(mockReviews);

      // 初始化默认规格
      if (mockProduct.specifications) {
        const defaultSpecs: { [specId: string]: string } = {};
        mockProduct.specifications.forEach(spec => {
          defaultSpecs[spec.id] = spec.options[0].id;
        });
        setSelectedSpecs(defaultSpecs);
      }
    }
  }, [id]);

  const handleSpecChange = (specId: string, optionId: string) => {
    setSelectedSpecs(prev => ({
      ...prev,
      [specId]: optionId
    }));
  };

  const calculateTotalPrice = () => {
    if (!product) return 0;
    let totalPrice = product.price;
    
    if (product.specifications) {
      product.specifications.forEach(spec => {
        const selectedOptionId = selectedSpecs[spec.id];
        const selectedOption = spec.options.find(option => option.id === selectedOptionId);
        if (selectedOption) {
          totalPrice += selectedOption.price;
        }
      });
    }
    
    return totalPrice * quantity;
  };

  const handleAddToCart = () => {
    if (!product) return;
    addItem(product, quantity, selectedSpecs);
    toast.success(`已添加 ${quantity} 份 ${product.name} 到购物车`);
  };

  const handleBuyNow = () => {
    if (!product) return;
    addItem(product, quantity, selectedSpecs);
    navigate('/employee/cart');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.name,
        text: product?.description,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('链接已复制到剪贴板');
    }
  };

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 顶部导航 */}
      <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between z-10">
        <button
          onClick={() => navigate(-1)}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <ArrowLeft className="w-6 h-6 text-gray-700" />
        </button>
        <h1 className="text-lg font-semibold text-gray-900">商品详情</h1>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsFavorite(!isFavorite)}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Heart className={`w-6 h-6 ${isFavorite ? 'text-red-500 fill-current' : 'text-gray-700'}`} />
          </button>
          <button
            onClick={handleShare}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Share2 className="w-6 h-6 text-gray-700" />
          </button>
        </div>
      </div>

      {/* 商品图片轮播 */}
      <div className="relative">
        <div className="aspect-square overflow-hidden">
          <img
            src={product.images?.[currentImageIndex] || product.image}
            alt={product.name}
            className="w-full h-full object-cover"
          />
        </div>
        
        {product.images && product.images.length > 1 && (
          <>
            {/* 图片指示器 */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              {product.images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                  }`}
                />
              ))}
            </div>
            
            {/* 缩略图 */}
            <div className="absolute bottom-4 right-4 flex space-x-2">
              {product.images.slice(0, 3).map((image, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-12 h-12 rounded-lg overflow-hidden border-2 transition-all ${
                    index === currentImageIndex ? 'border-white' : 'border-white border-opacity-50'
                  }`}
                >
                  <img src={image} alt={`${product.name} ${index + 1}`} className="w-full h-full object-cover" />
                </button>
              ))}
            </div>
          </>
        )}
        
        {/* 标签 */}
        <div className="absolute top-4 left-4 flex flex-wrap gap-2">
          {product.originalPrice && (
            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              特价
            </span>
          )}
          {product.sales > 100 && (
            <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
              热销
            </span>
          )}
          {product.tags?.map(tag => (
            <span key={tag} className="bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full">
              {tag}
            </span>
          ))}
        </div>
      </div>

      {/* 商品信息 */}
      <div className="p-4 space-y-4">
        {/* 基本信息 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{product.name}</h1>
          <p className="text-gray-600 leading-relaxed">{product.description}</p>
        </div>

        {/* 价格和评分 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-3xl font-bold text-red-500">¥{product.price}</span>
            {product.originalPrice && (
              <span className="text-lg text-gray-400 line-through">¥{product.originalPrice}</span>
            )}
          </div>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span>{product.rating}</span>
            </div>
            <span>销量 {product.sales}</span>
            <span>库存 {product.stock}</span>
          </div>
        </div>

        {/* 规格选择 */}
        {product.specifications && product.specifications.map(spec => (
          <div key={spec.id} className="space-y-3">
            <h3 className="font-semibold text-gray-900">{spec.name}</h3>
            <div className="flex flex-wrap gap-3">
              {spec.options.map(option => {
                const isSelected = selectedSpecs[spec.id] === option.id;
                return (
                  <button
                    key={option.id}
                    onClick={() => handleSpecChange(spec.id, option.id)}
                    className={`px-4 py-2 rounded-lg border transition-all ${
                      isSelected
                        ? 'border-orange-500 bg-orange-50 text-orange-600'
                        : 'border-gray-300 text-gray-700 hover:border-gray-400'
                    }`}
                  >
                    <span>{option.name}</span>
                    {option.price > 0 && (
                      <span className="ml-1 text-sm">+¥{option.price}</span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        ))}

        {/* 数量选择 */}
        <div className="flex items-center justify-between">
          <span className="font-semibold text-gray-900">数量</span>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setQuantity(Math.max(1, quantity - 1))}
              className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
            >
              <Minus className="w-4 h-4 text-gray-600" />
            </button>
            <span className="text-lg font-semibold w-8 text-center">{quantity}</span>
            <button
              onClick={() => setQuantity(quantity + 1)}
              className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
            >
              <Plus className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* 评价区域 */}
      <div className="bg-gray-50 p-4 space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">用户评价</h2>
          <span className="text-sm text-gray-600">{reviews.length} 条评价</span>
        </div>
        
        {reviews.slice(0, 3).map(review => (
          <div key={review.id} className="bg-white rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <img
                src={review.userAvatar || 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=default%20avatar%20icon&image_size=square'}
                alt={review.userName}
                className="w-10 h-10 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900">{review.userName}</span>
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${
                          i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date(review.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
            <p className="text-gray-700 mb-3">{review.content}</p>
            {review.images && review.images.length > 0 && (
              <div className="flex space-x-2 mb-3">
                {review.images.map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt={`评价图片 ${index + 1}`}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                ))}
              </div>
            )}
            {review.merchantReply && (
              <div className="bg-gray-50 rounded-lg p-3 mt-3">
                <div className="text-sm text-gray-600 mb-1">商家回复：</div>
                <p className="text-sm text-gray-700">{review.merchantReply}</p>
              </div>
            )}
          </div>
        ))}
        
        {reviews.length > 3 && (
          <button className="w-full py-3 text-orange-600 font-medium hover:bg-white rounded-lg transition-colors">
            查看全部评价
          </button>
        )}
      </div>

      {/* 底部操作栏 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div className="flex items-center space-x-3">
          <div className="flex-1">
            <div className="text-sm text-gray-600">总价</div>
            <div className="text-2xl font-bold text-red-500">¥{calculateTotalPrice().toFixed(2)}</div>
          </div>
          <button
            onClick={handleAddToCart}
            className="flex items-center space-x-2 bg-orange-100 text-orange-600 px-6 py-3 rounded-full font-medium hover:bg-orange-200 transition-colors"
          >
            <ShoppingCart className="w-5 h-5" />
            <span>加入购物车</span>
          </button>
          <button
            onClick={handleBuyNow}
            className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-3 rounded-full font-medium hover:from-orange-600 hover:to-red-600 transition-all"
          >
            立即购买
          </button>
        </div>
      </div>

      {/* 底部占位 */}
      <div className="h-20"></div>
    </div>
  );
};

export default ProductDetail;