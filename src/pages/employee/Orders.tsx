import { useState } from 'react';
import { Clock, Package, Truck, CheckCircle, XCircle, Star, MessageCircle, Phone, MapPin } from 'lucide-react';
import { Order, OrderStatus } from '../../types';
import { toast } from 'sonner';

const Orders = () => {
  const [activeTab, setActiveTab] = useState<'all' | OrderStatus>('all');
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [rating, setRating] = useState(5);
  const [reviewText, setReviewText] = useState('');

  // 模拟订单数据
  const mockOrders: Order[] = [
    {
      id: '1',
      orderNumber: 'DD202412150001',
      userId: 'user1',
      merchantId: 'merchant1',
      merchantName: '美味餐厅',
      items: [
        {
          id: '1',
          productId: 'product1',
          productName: '宫保鸡丁套餐',
          productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=delicious%20kung%20pao%20chicken%20set%20meal%20with%20rice%20and%20vegetables%20on%20white%20plate&image_size=square',
          quantity: 1,
          price: 28.00,
          totalPrice: 28.00,
          selectedSpecs: { '口味': '微辣', '主食': '米饭' }
        }
      ],
      totalAmount: 28.00,
      mealAllowanceUsed: 25.00,
      personalPayment: 3.00,
      status: 'delivered',
      paymentMethod: 'meal_allowance',
      deliveryAddress: {
        id: '1',
        userId: 'user1',
        name: '张三',
        phone: '138****5678',
        address: '北京市朝阳区建国门外大街1号国贸大厦A座15层',
        isDefault: true
      },
      deliveryTime: '2024-12-15 12:30',
      estimatedDeliveryTime: '2024-12-15 12:30',
      actualDeliveryTime: '2024-12-15 12:25',
      createdAt: '2024-12-15 11:45',
      updatedAt: '2024-12-15 12:25',
      deliveryPersonName: '李师傅',
      deliveryPersonPhone: '139****1234',
      remark: '不要香菜'
    },
    {
      id: '2',
      orderNumber: 'DD202412150002',
      userId: 'user1',
      merchantId: 'merchant2',
      merchantName: '健康轻食',
      items: [
        {
          id: '2',
          productId: 'product2',
          productName: '凯撒沙拉',
          productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=fresh%20caesar%20salad%20with%20lettuce%20croutons%20and%20dressing%20in%20bowl&image_size=square',
          quantity: 1,
          price: 22.00,
          totalPrice: 22.00,
          selectedSpecs: { '酱料': '凯撒酱', '配菜': '培根粒' }
        }
      ],
      totalAmount: 22.00,
      mealAllowanceUsed: 22.00,
      personalPayment: 0,
      status: 'delivering',
      paymentMethod: 'meal_allowance',
      deliveryAddress: {
        id: '1',
        userId: 'user1',
        name: '张三',
        phone: '138****5678',
        address: '北京市朝阳区建国门外大街1号国贸大厦A座15层',
        isDefault: true
      },
      deliveryTime: '2024-12-15 13:00',
      estimatedDeliveryTime: '2024-12-15 13:00',
      createdAt: '2024-12-15 12:15',
      updatedAt: '2024-12-15 12:45',
      deliveryPersonName: '王师傅',
      deliveryPersonPhone: '138****5678'
    },
    {
      id: '3',
      orderNumber: 'DD202412150003',
      userId: 'user1',
      merchantId: 'merchant1',
      merchantName: '美味餐厅',
      items: [
        {
          id: '3',
          productId: 'product3',
          productName: '红烧肉套餐',
          productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=braised%20pork%20belly%20set%20meal%20with%20rice%20and%20vegetables%20chinese%20style&image_size=square',
          quantity: 1,
          price: 32.00,
          totalPrice: 32.00,
          selectedSpecs: { '口味': '正常', '主食': '米饭' }
        }
      ],
      totalAmount: 32.00,
      mealAllowanceUsed: 25.00,
      personalPayment: 7.00,
      status: 'preparing',
      paymentMethod: 'meal_allowance',
      deliveryAddress: {
        id: '1',
        userId: 'user1',
        name: '张三',
        phone: '138****5678',
        address: '北京市朝阳区建国门外大街1号国贸大厦A座15层',
        isDefault: true
      },
      deliveryTime: '2024-12-15 13:30',
      estimatedDeliveryTime: '2024-12-15 13:30',
      createdAt: '2024-12-15 12:45',
      updatedAt: '2024-12-15 12:45'
    }
  ];

  const tabs = [
    { key: 'all', label: '全部', count: mockOrders.length },
    { key: 'pending', label: '待付款', count: mockOrders.filter(o => o.status === 'pending').length },
    { key: 'preparing', label: '制作中', count: mockOrders.filter(o => o.status === 'preparing').length },
    { key: 'delivering', label: '配送中', count: mockOrders.filter(o => o.status === 'delivering').length },
    { key: 'delivered', label: '已完成', count: mockOrders.filter(o => o.status === 'delivered').length }
  ];

  const filteredOrders = activeTab === 'all' 
    ? mockOrders 
    : mockOrders.filter(order => order.status === activeTab);

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-orange-500" />;
      case 'preparing':
        return <Package className="w-5 h-5 text-blue-500" />;
      case 'delivering':
        return <Truck className="w-5 h-5 text-purple-500" />;
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return '待付款';
      case 'preparing':
        return '制作中';
      case 'delivering':
        return '配送中';
      case 'delivered':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return 'text-orange-600 bg-orange-50';
      case 'preparing':
        return 'text-blue-600 bg-blue-50';
      case 'delivering':
        return 'text-purple-600 bg-purple-50';
      case 'delivered':
        return 'text-green-600 bg-green-50';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const handleCancelOrder = (orderId: string) => {
    toast.success('订单已取消');
  };

  const handleContactDelivery = (phone: string) => {
    toast.info(`拨打电话：${phone}`);
  };

  const handleReorder = (order: Order) => {
    toast.success('已添加到购物车');
  };

  const handleReview = (order: Order) => {
    setSelectedOrder(order);
    setShowReviewModal(true);
  };

  const submitReview = () => {
    if (!selectedOrder) return;
    
    toast.success('评价提交成功');
    setShowReviewModal(false);
    setSelectedOrder(null);
    setRating(5);
    setReviewText('');
  };

  const formatSpecsText = (selectedSpecs?: { [specId: string]: string }) => {
    if (!selectedSpecs || Object.keys(selectedSpecs).length === 0) {
      return '';
    }
    return Object.values(selectedSpecs).join('，');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200">
        <h1 className="text-lg font-semibold text-gray-900 px-4 py-3">我的订单</h1>
        
        {/* 标签页 */}
        <div className="flex overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex-shrink-0 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.key
                  ? 'text-orange-600 border-orange-600'
                  : 'text-gray-600 border-transparent hover:text-gray-900'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className={`ml-1 px-1.5 py-0.5 text-xs rounded-full ${
                  activeTab === tab.key
                    ? 'bg-orange-100 text-orange-600'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* 订单列表 */}
      <div className="p-4 space-y-4">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <Package className="w-12 h-12 text-gray-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">暂无订单</h2>
            <p className="text-gray-600">您还没有相关订单</p>
          </div>
        ) : (
          filteredOrders.map((order) => (
            <div key={order.id} className="bg-white rounded-2xl shadow-sm overflow-hidden">
              {/* 订单头部 */}
              <div className="px-4 py-3 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">{order.merchantName}</span>
                    <span className="text-xs text-gray-500">#{order.orderNumber}</span>
                  </div>
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                    getStatusColor(order.status)
                  }`}>
                    {getStatusIcon(order.status)}
                    <span>{getStatusText(order.status)}</span>
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  下单时间：{order.createdAt}
                </div>
              </div>

              {/* 商品列表 */}
              <div className="px-4 py-3">
                {order.items.map((item) => {
                  const specsText = formatSpecsText(item.selectedSpecs);
                  return (
                    <div key={item.id} className="flex items-center space-x-3 mb-3 last:mb-0">
                      <img
                        src={item.productImage}
                        alt={item.productName}
                        className="w-16 h-16 rounded-xl object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">{item.productName}</h3>
                        {specsText && (
                          <p className="text-sm text-gray-500 mt-1">{specsText}</p>
                        )}
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-sm font-semibold text-red-500">
                            ¥{item.price.toFixed(2)}
                          </span>
                          <span className="text-sm text-gray-500">x{item.quantity}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* 配送信息 */}
              {(order.status === 'delivering' || order.status === 'delivered') && (
                <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
                  <div className="flex items-start space-x-3">
                    <MapPin className="w-4 h-4 text-gray-500 mt-0.5" />
                    <div className="flex-1 text-sm">
                      <div className="text-gray-900 font-medium">
                        {order.deliveryAddress.name} {order.deliveryAddress.phone}
                      </div>
                      <div className="text-gray-600 mt-1">
                        {order.deliveryAddress.address}
                      </div>
                    </div>
                  </div>
                  
                  {order.deliveryPersonName && (
                    <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">配送员：</span>
                        <span className="text-sm font-medium text-gray-900">
                          {order.deliveryPersonName}
                        </span>
                      </div>
                      {order.deliveryPersonPhone && (
                        <button
                          onClick={() => handleContactDelivery(order.deliveryPersonPhone!)}
                          className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700"
                        >
                          <Phone className="w-4 h-4" />
                          <span>联系</span>
                        </button>
                      )}
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-500 mt-2">
                    {order.status === 'delivering' 
                      ? `预计送达：${order.estimatedDeliveryTime}`
                      : `送达时间：${order.actualDeliveryTime}`
                    }
                  </div>
                </div>
              )}

              {/* 订单金额 */}
              <div className="px-4 py-3 border-t border-gray-100">
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between text-gray-600">
                    <span>商品总额</span>
                    <span>¥{order.totalAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-orange-600">
                    <span>餐标抵扣</span>
                    <span>-¥{order.mealAllowanceUsed.toFixed(2)}</span>
                  </div>
                  {order.personalPayment > 0 && (
                    <div className="flex justify-between text-gray-900 font-semibold">
                      <span>个人支付</span>
                      <span>¥{order.personalPayment.toFixed(2)}</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                  <div className="text-lg font-bold text-red-500">
                    实付：¥{order.totalAmount.toFixed(2)}
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2">
                    {order.status === 'pending' && (
                      <>
                        <button
                          onClick={() => handleCancelOrder(order.id)}
                          className="px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-full hover:bg-gray-50 transition-colors"
                        >
                          取消订单
                        </button>
                        <button className="px-4 py-2 text-sm text-white bg-gradient-to-r from-orange-500 to-red-500 rounded-full hover:from-orange-600 hover:to-red-600 transition-all">
                          立即付款
                        </button>
                      </>
                    )}
                    
                    {order.status === 'delivered' && (
                      <>
                        <button
                          onClick={() => handleReorder(order)}
                          className="px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-full hover:bg-gray-50 transition-colors"
                        >
                          再来一单
                        </button>
                        <button
                          onClick={() => handleReview(order)}
                          className="px-4 py-2 text-sm text-white bg-gradient-to-r from-orange-500 to-red-500 rounded-full hover:from-orange-600 hover:to-red-600 transition-all flex items-center space-x-1"
                        >
                          <Star className="w-4 h-4" />
                          <span>评价</span>
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* 评价弹窗 */}
      {showReviewModal && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl w-full max-w-md max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">评价订单</h2>
              
              {/* 商品信息 */}
              <div className="mb-6">
                <div className="text-sm text-gray-600 mb-2">{selectedOrder.merchantName}</div>
                {selectedOrder.items.map((item) => (
                  <div key={item.id} className="flex items-center space-x-3">
                    <img
                      src={item.productImage}
                      alt={item.productName}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{item.productName}</div>
                      <div className="text-sm text-gray-500">x{item.quantity}</div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* 评分 */}
              <div className="mb-6">
                <div className="text-sm font-medium text-gray-900 mb-3">整体评价</div>
                <div className="flex items-center space-x-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => setRating(star)}
                      className={`w-8 h-8 ${
                        star <= rating ? 'text-yellow-400' : 'text-gray-300'
                      } hover:text-yellow-400 transition-colors`}
                    >
                      <Star className="w-full h-full fill-current" />
                    </button>
                  ))}
                  <span className="text-sm text-gray-600 ml-2">
                    {rating === 5 ? '非常满意' : rating === 4 ? '满意' : rating === 3 ? '一般' : rating === 2 ? '不满意' : '非常不满意'}
                  </span>
                </div>
              </div>
              
              {/* 评价内容 */}
              <div className="mb-6">
                <div className="text-sm font-medium text-gray-900 mb-3">评价内容</div>
                <textarea
                  value={reviewText}
                  onChange={(e) => setReviewText(e.target.value)}
                  placeholder="分享您的用餐体验..."
                  className="w-full h-24 p-3 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
              
              {/* 按钮 */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowReviewModal(false)}
                  className="flex-1 py-3 text-gray-600 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={submitReview}
                  className="flex-1 py-3 text-white bg-gradient-to-r from-orange-500 to-red-500 rounded-xl hover:from-orange-600 hover:to-red-600 transition-all"
                >
                  提交评价
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Orders;