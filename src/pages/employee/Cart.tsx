import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Minus, Plus, Trash2, ShoppingBag, CreditCard, Wallet } from 'lucide-react';
import { useCartStore, useUserStore } from '../../store';
import { toast } from 'sonner';

const Cart = () => {
  const navigate = useNavigate();
  const { items, updateQuantity, removeItem, clearCart, getTotalPrice } = useCartStore();
  const { user } = useUserStore();
  const [selectedItems, setSelectedItems] = useState<string[]>(items.map(item => item.id));
  const [paymentMethod, setPaymentMethod] = useState<'meal_allowance' | 'wallet' | 'wechat' | 'alipay'>('meal_allowance');
  const [mealAllowanceBalance] = useState(25.00); // 模拟餐标余额
  const [walletBalance] = useState(156.80); // 模拟钱包余额

  const selectedItemsData = items.filter(item => selectedItems.includes(item.id));
  const selectedTotalPrice = selectedItemsData.reduce((sum, item) => sum + item.totalPrice, 0);
  const mealAllowanceUsed = Math.min(selectedTotalPrice, mealAllowanceBalance);
  const personalPayment = Math.max(0, selectedTotalPrice - mealAllowanceUsed);

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === items.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(items.map(item => item.id));
    }
  };

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handleRemoveItem = (itemId: string) => {
    removeItem(itemId);
    setSelectedItems(prev => prev.filter(id => id !== itemId));
    toast.success('商品已移除');
  };

  const handleClearCart = () => {
    clearCart();
    setSelectedItems([]);
    toast.success('购物车已清空');
  };

  const handleCheckout = () => {
    if (selectedItems.length === 0) {
      toast.error('请选择要结算的商品');
      return;
    }

    if (personalPayment > 0 && paymentMethod === 'wallet' && personalPayment > walletBalance) {
      toast.error('钱包余额不足');
      return;
    }

    // 模拟下单
    toast.success('订单提交成功！');
    
    // 清除已选商品
    selectedItems.forEach(itemId => removeItem(itemId));
    setSelectedItems([]);
    
    // 跳转到订单页面
    navigate('/employee/orders');
  };

  const formatSpecsText = (selectedSpecs?: { [specId: string]: string }) => {
    if (!selectedSpecs || Object.keys(selectedSpecs).length === 0) {
      return '';
    }
    return Object.values(selectedSpecs).join('，');
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
        <div className="text-center">
          <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <ShoppingBag className="w-12 h-12 text-gray-400" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">购物车是空的</h2>
          <p className="text-gray-600 mb-6">快去选购您喜欢的美食吧</p>
          <button
            onClick={() => navigate('/employee')}
            className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-3 rounded-full font-medium hover:from-orange-600 hover:to-red-600 transition-all"
          >
            去逛逛
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-32">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-lg font-semibold text-gray-900">购物车</h1>
          <button
            onClick={handleClearCart}
            className="text-gray-600 hover:text-red-500 transition-colors"
          >
            <Trash2 className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 全选 */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <label className="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            checked={selectedItems.length === items.length && items.length > 0}
            onChange={handleSelectAll}
            className="w-5 h-5 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
          />
          <span className="text-gray-900 font-medium">全选</span>
          <span className="text-gray-500">({items.length}件商品)</span>
        </label>
      </div>

      {/* 商品列表 */}
      <div className="space-y-2 p-4">
        {items.map((item) => {
          const isSelected = selectedItems.includes(item.id);
          const specsText = formatSpecsText(item.selectedSpecs);
          
          return (
            <div key={item.id} className="bg-white rounded-2xl p-4 shadow-sm">
              <div className="flex items-start space-x-3">
                {/* 选择框 */}
                <label className="flex items-center mt-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => handleSelectItem(item.id)}
                    className="w-5 h-5 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
                  />
                </label>

                {/* 商品图片 */}
                <img
                  src={item.product.image}
                  alt={item.product.name}
                  className="w-20 h-20 rounded-xl object-cover"
                />

                {/* 商品信息 */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gray-900 mb-1 truncate">
                    {item.product.name}
                  </h3>
                  {specsText && (
                    <p className="text-sm text-gray-500 mb-2">{specsText}</p>
                  )}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-red-500">
                        ¥{item.price.toFixed(2)}
                      </span>
                      {item.product.originalPrice && item.product.originalPrice > item.price && (
                        <span className="text-sm text-gray-400 line-through">
                          ¥{item.product.originalPrice.toFixed(2)}
                        </span>
                      )}
                    </div>
                    
                    {/* 数量控制 */}
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
                      >
                        <Minus className="w-4 h-4 text-gray-600" />
                      </button>
                      <span className="text-lg font-semibold w-8 text-center">
                        {item.quantity}
                      </span>
                      <button
                        onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
                      >
                        <Plus className="w-4 h-4 text-gray-600" />
                      </button>
                    </div>
                  </div>
                  
                  {/* 小计 */}
                  <div className="flex items-center justify-between mt-2">
                    <button
                      onClick={() => handleRemoveItem(item.id)}
                      className="text-sm text-gray-500 hover:text-red-500 transition-colors"
                    >
                      移除
                    </button>
                    <span className="text-sm text-gray-600">
                      小计：¥{item.totalPrice.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 餐标和支付信息 */}
      {selectedItems.length > 0 && (
        <div className="bg-white mx-4 rounded-2xl p-4 space-y-4">
          {/* 餐标使用 */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-900">餐标抵扣</h3>
            <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">可用餐标余额</span>
                <span className="font-semibold text-orange-600">¥{mealAllowanceBalance.toFixed(2)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">本次使用</span>
                <span className="font-semibold text-orange-600">-¥{mealAllowanceUsed.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* 个人支付 */}
          {personalPayment > 0 && (
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">个人支付</h3>
              <div className="space-y-2">
                <label className="flex items-center space-x-3 p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="payment"
                    value="wallet"
                    checked={paymentMethod === 'wallet'}
                    onChange={(e) => setPaymentMethod(e.target.value as any)}
                    className="w-4 h-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                  />
                  <Wallet className="w-5 h-5 text-blue-500" />
                  <div className="flex-1">
                    <span className="font-medium text-gray-900">钱包余额</span>
                    <div className="text-sm text-gray-500">余额：¥{walletBalance.toFixed(2)}</div>
                  </div>
                </label>
                
                <label className="flex items-center space-x-3 p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="payment"
                    value="wechat"
                    checked={paymentMethod === 'wechat'}
                    onChange={(e) => setPaymentMethod(e.target.value as any)}
                    className="w-4 h-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                  />
                  <div className="w-5 h-5 bg-green-500 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">微</span>
                  </div>
                  <span className="font-medium text-gray-900">微信支付</span>
                </label>
                
                <label className="flex items-center space-x-3 p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="payment"
                    value="alipay"
                    checked={paymentMethod === 'alipay'}
                    onChange={(e) => setPaymentMethod(e.target.value as any)}
                    className="w-4 h-4 text-orange-500 border-gray-300 focus:ring-orange-500"
                  />
                  <div className="w-5 h-5 bg-blue-500 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">支</span>
                  </div>
                  <span className="font-medium text-gray-900">支付宝</span>
                </label>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 底部结算栏 */}
      {selectedItems.length > 0 && (
        <div className="fixed bottom-20 left-0 right-0 bg-white border-t border-gray-200 p-4">
          <div className="space-y-3">
            {/* 费用明细 */}
            <div className="space-y-1 text-sm">
              <div className="flex justify-between text-gray-600">
                <span>商品总额</span>
                <span>¥{selectedTotalPrice.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-orange-600">
                <span>餐标抵扣</span>
                <span>-¥{mealAllowanceUsed.toFixed(2)}</span>
              </div>
              {personalPayment > 0 && (
                <div className="flex justify-between text-gray-900 font-semibold">
                  <span>个人支付</span>
                  <span>¥{personalPayment.toFixed(2)}</span>
                </div>
              )}
            </div>
            
            {/* 结算按钮 */}
            <div className="flex items-center space-x-3">
              <div className="flex-1">
                <div className="text-sm text-gray-600">已选 {selectedItems.length} 件</div>
                <div className="text-lg font-bold text-red-500">
                  合计：¥{selectedTotalPrice.toFixed(2)}
                </div>
              </div>
              <button
                onClick={handleCheckout}
                disabled={personalPayment > 0 && paymentMethod === 'wallet' && personalPayment > walletBalance}
                className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-3 rounded-full font-medium hover:from-orange-600 hover:to-red-600 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                <CreditCard className="w-5 h-5" />
                <span>去结算</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Cart;