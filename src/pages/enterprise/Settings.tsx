import { useState } from 'react';
import { 
  Building2, 
  Mail, 
  Phone, 
  MapPin, 
  User, 
  Shield, 
  Bell, 
  Palette, 
  Globe, 
  Database, 
  Key, 
  Save, 
  Upload, 
  Eye, 
  EyeOff,
  Check,
  X,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface EnterpriseInfo {
  name: string;
  code: string;
  industry: string;
  scale: string;
  address: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  description: string;
  logo?: string;
}

interface SystemSettings {
  timezone: string;
  language: string;
  currency: string;
  dateFormat: string;
  workDays: string[];
  workStartTime: string;
  workEndTime: string;
  mealTimeSlots: {
    breakfast: { start: string; end: string; enabled: boolean };
    lunch: { start: string; end: string; enabled: boolean };
    dinner: { start: string; end: string; enabled: boolean };
    snack: { start: string; end: string; enabled: boolean };
  };
}

interface NotificationSettings {
  emailNotifications: boolean;
  smsNotifications: boolean;
  orderNotifications: boolean;
  paymentNotifications: boolean;
  systemNotifications: boolean;
  weeklyReports: boolean;
  monthlyReports: boolean;
}

interface SecuritySettings {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSymbols: boolean;
    expiryDays: number;
  };
  sessionTimeout: number;
  twoFactorAuth: boolean;
  ipWhitelist: string[];
  loginAttempts: number;
}

const Settings = () => {
  const [activeTab, setActiveTab] = useState<'basic' | 'system' | 'notification' | 'security' | 'integration'>('basic');
  const [showPassword, setShowPassword] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // 企业基本信息
  const [enterpriseInfo, setEnterpriseInfo] = useState<EnterpriseInfo>({
    name: '科技创新有限公司',
    code: 'TECH2024',
    industry: '信息技术',
    scale: '100-500人',
    address: '北京市朝阳区科技园区创新大厦A座15层',
    contactPerson: '张经理',
    contactPhone: '010-12345678',
    contactEmail: '<EMAIL>',
    description: '专注于企业数字化转型的科技公司，致力于为企业提供高效的管理解决方案。',
    logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20tech%20company%20logo%20blue%20gradient&image_size=square'
  });

  // 系统设置
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    currency: 'CNY',
    dateFormat: 'YYYY-MM-DD',
    workDays: ['周一', '周二', '周三', '周四', '周五'],
    workStartTime: '09:00',
    workEndTime: '18:00',
    mealTimeSlots: {
      breakfast: { start: '07:00', end: '10:00', enabled: true },
      lunch: { start: '11:00', end: '14:00', enabled: true },
      dinner: { start: '17:00', end: '20:00', enabled: true },
      snack: { start: '14:00', end: '17:00', enabled: false }
    }
  });

  // 通知设置
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    smsNotifications: false,
    orderNotifications: true,
    paymentNotifications: true,
    systemNotifications: true,
    weeklyReports: true,
    monthlyReports: true
  });

  // 安全设置
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSymbols: false,
      expiryDays: 90
    },
    sessionTimeout: 30,
    twoFactorAuth: false,
    ipWhitelist: [],
    loginAttempts: 5
  });

  const industries = [
    '信息技术', '制造业', '金融服务', '教育培训', '医疗健康',
    '零售贸易', '房地产', '交通运输', '能源化工', '其他'
  ];

  const scales = [
    '1-50人', '51-100人', '101-500人', '501-1000人', '1000人以上'
  ];

  const timezones = [
    { value: 'Asia/Shanghai', label: '北京时间 (UTC+8)' },
    { value: 'Asia/Hong_Kong', label: '香港时间 (UTC+8)' },
    { value: 'Asia/Tokyo', label: '东京时间 (UTC+9)' },
    { value: 'America/New_York', label: '纽约时间 (UTC-5)' },
    { value: 'Europe/London', label: '伦敦时间 (UTC+0)' }
  ];

  const languages = [
    { value: 'zh-CN', label: '简体中文' },
    { value: 'zh-TW', label: '繁体中文' },
    { value: 'en-US', label: 'English' },
    { value: 'ja-JP', label: '日本語' },
    { value: 'ko-KR', label: '한국어' }
  ];

  const currencies = [
    { value: 'CNY', label: '人民币 (¥)' },
    { value: 'USD', label: '美元 ($)' },
    { value: 'EUR', label: '欧元 (€)' },
    { value: 'JPY', label: '日元 (¥)' },
    { value: 'HKD', label: '港币 (HK$)' }
  ];

  const handleSave = () => {
    toast.success('设置保存成功');
    setIsEditing(false);
  };

  const handleUploadLogo = () => {
    toast.success('企业Logo上传成功');
  };

  const handleTestNotification = () => {
    toast.success('测试通知发送成功');
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">企业设置</h1>
          <p className="text-gray-600 mt-1">管理企业基本信息和系统配置</p>
        </div>
        <div className="flex items-center space-x-3">
          {isEditing ? (
            <>
              <button
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2"
              >
                <X className="w-4 h-4" />
                <span>取消</span>
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>保存</span>
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <User className="w-4 h-4" />
              <span>编辑设置</span>
            </button>
          )}
        </div>
      </div>

      {/* 标签页 */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('basic')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'basic'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              基本信息
            </button>
            <button
              onClick={() => setActiveTab('system')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'system'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              系统设置
            </button>
            <button
              onClick={() => setActiveTab('notification')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'notification'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              通知设置
            </button>
            <button
              onClick={() => setActiveTab('security')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'security'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              安全设置
            </button>
            <button
              onClick={() => setActiveTab('integration')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'integration'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              集成配置
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">企业基本信息</h3>
              
              {/* 企业Logo */}
              <div className="flex items-center space-x-6">
                <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                  {enterpriseInfo.logo ? (
                    <img src={enterpriseInfo.logo} alt="企业Logo" className="w-full h-full object-cover" />
                  ) : (
                    <Building2 className="w-12 h-12 text-gray-400" />
                  )}
                </div>
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-2">企业Logo</h4>
                  <p className="text-sm text-gray-600 mb-3">建议尺寸: 200x200px，支持 JPG、PNG 格式</p>
                  <button
                    onClick={handleUploadLogo}
                    disabled={!isEditing}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    <Upload className="w-4 h-4" />
                    <span>上传Logo</span>
                  </button>
                </div>
              </div>

              {/* 基本信息表单 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    企业名称 *
                  </label>
                  <input
                    type="text"
                    value={enterpriseInfo.name}
                    onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, name: e.target.value })}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    企业代码 *
                  </label>
                  <input
                    type="text"
                    value={enterpriseInfo.code}
                    onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, code: e.target.value })}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    所属行业
                  </label>
                  <select
                    value={enterpriseInfo.industry}
                    onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, industry: e.target.value })}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                  >
                    {industries.map(industry => (
                      <option key={industry} value={industry}>{industry}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    企业规模
                  </label>
                  <select
                    value={enterpriseInfo.scale}
                    onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, scale: e.target.value })}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                  >
                    {scales.map(scale => (
                      <option key={scale} value={scale}>{scale}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    联系人
                  </label>
                  <input
                    type="text"
                    value={enterpriseInfo.contactPerson}
                    onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, contactPerson: e.target.value })}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    联系电话
                  </label>
                  <input
                    type="tel"
                    value={enterpriseInfo.contactPhone}
                    onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, contactPhone: e.target.value })}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系邮箱
                </label>
                <input
                  type="email"
                  value={enterpriseInfo.contactEmail}
                  onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, contactEmail: e.target.value })}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  企业地址
                </label>
                <input
                  type="text"
                  value={enterpriseInfo.address}
                  onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, address: e.target.value })}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  企业简介
                </label>
                <textarea
                  value={enterpriseInfo.description}
                  onChange={(e) => setEnterpriseInfo({ ...enterpriseInfo, description: e.target.value })}
                  disabled={!isEditing}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                />
              </div>
            </div>
          )}

          {activeTab === 'system' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">系统设置</h3>
              
              {/* 基础设置 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">基础配置</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      时区设置
                    </label>
                    <select
                      value={systemSettings.timezone}
                      onChange={(e) => setSystemSettings({ ...systemSettings, timezone: e.target.value })}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    >
                      {timezones.map(tz => (
                        <option key={tz.value} value={tz.value}>{tz.label}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      系统语言
                    </label>
                    <select
                      value={systemSettings.language}
                      onChange={(e) => setSystemSettings({ ...systemSettings, language: e.target.value })}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    >
                      {languages.map(lang => (
                        <option key={lang.value} value={lang.value}>{lang.label}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      货币单位
                    </label>
                    <select
                      value={systemSettings.currency}
                      onChange={(e) => setSystemSettings({ ...systemSettings, currency: e.target.value })}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    >
                      {currencies.map(currency => (
                        <option key={currency.value} value={currency.value}>{currency.label}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      日期格式
                    </label>
                    <select
                      value={systemSettings.dateFormat}
                      onChange={(e) => setSystemSettings({ ...systemSettings, dateFormat: e.target.value })}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    >
                      <option value="YYYY-MM-DD">2024-12-15</option>
                      <option value="DD/MM/YYYY">15/12/2024</option>
                      <option value="MM/DD/YYYY">12/15/2024</option>
                      <option value="DD-MM-YYYY">15-12-2024</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* 工作时间设置 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">工作时间配置</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      工作日
                    </label>
                    <div className="grid grid-cols-4 gap-2">
                      {['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map(day => (
                        <label key={day} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={systemSettings.workDays.includes(day)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSystemSettings({
                                  ...systemSettings,
                                  workDays: [...systemSettings.workDays, day]
                                });
                              } else {
                                setSystemSettings({
                                  ...systemSettings,
                                  workDays: systemSettings.workDays.filter(d => d !== day)
                                });
                              }
                            }}
                            disabled={!isEditing}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                          />
                          <span className="ml-1 text-sm text-gray-700">{day}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        工作时间
                      </label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="time"
                          value={systemSettings.workStartTime}
                          onChange={(e) => setSystemSettings({ ...systemSettings, workStartTime: e.target.value })}
                          disabled={!isEditing}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                        />
                        <span className="text-gray-500">至</span>
                        <input
                          type="time"
                          value={systemSettings.workEndTime}
                          onChange={(e) => setSystemSettings({ ...systemSettings, workEndTime: e.target.value })}
                          disabled={!isEditing}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 用餐时段设置 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">用餐时段配置</h4>
                <div className="space-y-4">
                  {Object.entries(systemSettings.mealTimeSlots).map(([key, slot]) => {
                    const mealNames = {
                      breakfast: '早餐',
                      lunch: '午餐',
                      dinner: '晚餐',
                      snack: '下午茶'
                    };
                    
                    return (
                      <div key={key} className="flex items-center space-x-4">
                        <label className="flex items-center w-20">
                          <input
                            type="checkbox"
                            checked={slot.enabled}
                            onChange={(e) => setSystemSettings({
                              ...systemSettings,
                              mealTimeSlots: {
                                ...systemSettings.mealTimeSlots,
                                [key]: { ...slot, enabled: e.target.checked }
                              }
                            })}
                            disabled={!isEditing}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                          />
                          <span className="ml-2 text-sm font-medium text-gray-700">
                            {mealNames[key as keyof typeof mealNames]}
                          </span>
                        </label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="time"
                            value={slot.start}
                            onChange={(e) => setSystemSettings({
                              ...systemSettings,
                              mealTimeSlots: {
                                ...systemSettings.mealTimeSlots,
                                [key]: { ...slot, start: e.target.value }
                              }
                            })}
                            disabled={!isEditing || !slot.enabled}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                          />
                          <span className="text-gray-500">至</span>
                          <input
                            type="time"
                            value={slot.end}
                            onChange={(e) => setSystemSettings({
                              ...systemSettings,
                              mealTimeSlots: {
                                ...systemSettings.mealTimeSlots,
                                [key]: { ...slot, end: e.target.value }
                              }
                            })}
                            disabled={!isEditing || !slot.enabled}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notification' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">通知设置</h3>
                <button
                  onClick={handleTestNotification}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
                >
                  <Bell className="w-4 h-4" />
                  <span>测试通知</span>
                </button>
              </div>
              
              <div className="space-y-6">
                {/* 通知渠道 */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">通知渠道</h4>
                  <div className="space-y-4">
                    <label className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Mail className="w-5 h-5 text-gray-400" />
                        <div>
                          <span className="text-sm font-medium text-gray-700">邮件通知</span>
                          <p className="text-xs text-gray-500">通过邮件接收系统通知</p>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={notificationSettings.emailNotifications}
                        onChange={(e) => setNotificationSettings({
                          ...notificationSettings,
                          emailNotifications: e.target.checked
                        })}
                        disabled={!isEditing}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                      />
                    </label>
                    
                    <label className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Phone className="w-5 h-5 text-gray-400" />
                        <div>
                          <span className="text-sm font-medium text-gray-700">短信通知</span>
                          <p className="text-xs text-gray-500">通过短信接收重要通知</p>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={notificationSettings.smsNotifications}
                        onChange={(e) => setNotificationSettings({
                          ...notificationSettings,
                          smsNotifications: e.target.checked
                        })}
                        disabled={!isEditing}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                      />
                    </label>
                  </div>
                </div>
                
                {/* 通知类型 */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">通知类型</h4>
                  <div className="space-y-4">
                    {[
                      { key: 'orderNotifications', label: '订单通知', desc: '新订单、订单状态变更等' },
                      { key: 'paymentNotifications', label: '支付通知', desc: '支付成功、失败等' },
                      { key: 'systemNotifications', label: '系统通知', desc: '系统维护、更新等' }
                    ].map(item => (
                      <label key={item.key} className="flex items-center justify-between">
                        <div>
                          <span className="text-sm font-medium text-gray-700">{item.label}</span>
                          <p className="text-xs text-gray-500">{item.desc}</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={notificationSettings[item.key as keyof NotificationSettings] as boolean}
                          onChange={(e) => setNotificationSettings({
                            ...notificationSettings,
                            [item.key]: e.target.checked
                          })}
                          disabled={!isEditing}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                        />
                      </label>
                    ))}
                  </div>
                </div>
                
                {/* 报告设置 */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">定期报告</h4>
                  <div className="space-y-4">
                    <label className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium text-gray-700">周报</span>
                        <p className="text-xs text-gray-500">每周发送消费统计报告</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={notificationSettings.weeklyReports}
                        onChange={(e) => setNotificationSettings({
                          ...notificationSettings,
                          weeklyReports: e.target.checked
                        })}
                        disabled={!isEditing}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                      />
                    </label>
                    
                    <label className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium text-gray-700">月报</span>
                        <p className="text-xs text-gray-500">每月发送详细分析报告</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={notificationSettings.monthlyReports}
                        onChange={(e) => setNotificationSettings({
                          ...notificationSettings,
                          monthlyReports: e.target.checked
                        })}
                        disabled={!isEditing}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                      />
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">安全设置</h3>
              
              {/* 密码策略 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">密码策略</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      最小长度
                    </label>
                    <input
                      type="number"
                      min="6"
                      max="20"
                      value={securitySettings.passwordPolicy.minLength}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        passwordPolicy: {
                          ...securitySettings.passwordPolicy,
                          minLength: parseInt(e.target.value)
                        }
                      })}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      密码有效期 (天)
                    </label>
                    <input
                      type="number"
                      min="30"
                      max="365"
                      value={securitySettings.passwordPolicy.expiryDays}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        passwordPolicy: {
                          ...securitySettings.passwordPolicy,
                          expiryDays: parseInt(e.target.value)
                        }
                      })}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                </div>
                
                <div className="mt-4 space-y-3">
                  <h5 className="text-sm font-medium text-gray-700">密码复杂度要求</h5>
                  {[
                    { key: 'requireUppercase', label: '包含大写字母' },
                    { key: 'requireLowercase', label: '包含小写字母' },
                    { key: 'requireNumbers', label: '包含数字' },
                    { key: 'requireSymbols', label: '包含特殊字符' }
                  ].map(item => (
                    <label key={item.key} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={securitySettings.passwordPolicy[item.key as keyof typeof securitySettings.passwordPolicy] as boolean}
                        onChange={(e) => setSecuritySettings({
                          ...securitySettings,
                          passwordPolicy: {
                            ...securitySettings.passwordPolicy,
                            [item.key]: e.target.checked
                          }
                        })}
                        disabled={!isEditing}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                      />
                      <span className="ml-2 text-sm text-gray-700">{item.label}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              {/* 登录安全 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">登录安全</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      会话超时 (分钟)
                    </label>
                    <input
                      type="number"
                      min="5"
                      max="480"
                      value={securitySettings.sessionTimeout}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        sessionTimeout: parseInt(e.target.value)
                      })}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      最大登录尝试次数
                    </label>
                    <input
                      type="number"
                      min="3"
                      max="10"
                      value={securitySettings.loginAttempts}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        loginAttempts: parseInt(e.target.value)
                      })}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                </div>
                
                <div className="mt-4">
                  <label className="flex items-center justify-between">
                    <div>
                      <span className="text-sm font-medium text-gray-700">启用双因子认证</span>
                      <p className="text-xs text-gray-500">增强账户安全性</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={securitySettings.twoFactorAuth}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        twoFactorAuth: e.target.checked
                      })}
                      disabled={!isEditing}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                    />
                  </label>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'integration' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">集成配置</h3>
              
              {/* API配置 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">API配置</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API密钥
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        value="sk-1234567890abcdef"
                        readOnly
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                      />
                      <button
                        onClick={() => setShowPassword(!showPassword)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                      <button className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        重新生成
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Webhook URL
                    </label>
                    <input
                      type="url"
                      placeholder="https://your-domain.com/webhook"
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                    />
                  </div>
                </div>
              </div>
              
              {/* 第三方集成 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">第三方集成</h4>
                <div className="space-y-4">
                  {[
                    { name: '钉钉', status: 'connected', icon: '🔔' },
                    { name: '企业微信', status: 'disconnected', icon: '💬' },
                    { name: '飞书', status: 'disconnected', icon: '🚀' },
                    { name: '支付宝', status: 'connected', icon: '💰' }
                  ].map((integration, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-white rounded-lg border">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{integration.icon}</span>
                        <div>
                          <h5 className="text-sm font-medium text-gray-900">{integration.name}</h5>
                          <p className="text-xs text-gray-500">
                            {integration.status === 'connected' ? '已连接' : '未连接'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          integration.status === 'connected' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {integration.status === 'connected' ? (
                            <CheckCircle className="w-3 h-3 mr-1" />
                          ) : (
                            <XCircle className="w-3 h-3 mr-1" />
                          )}
                          {integration.status === 'connected' ? '已连接' : '未连接'}
                        </span>
                        <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                          {integration.status === 'connected' ? '配置' : '连接'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;