import { useState } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  ShoppingCart, 
  Calendar,
  Download,
  Filter,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';
import { toast } from 'sonner';

interface StatisticsData {
  totalConsumption: number;
  totalOrders: number;
  avgOrderValue: number;
  activeUsers: number;
  consumptionGrowth: number;
  orderGrowth: number;
}

interface DepartmentStats {
  department: string;
  consumption: number;
  orders: number;
  avgPerPerson: number;
  growth: number;
}

interface DailyConsumption {
  date: string;
  consumption: number;
  orders: number;
  users: number;
}

interface PopularItem {
  name: string;
  category: string;
  orders: number;
  revenue: number;
  growth: number;
}

const Statistics = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [activeTab, setActiveTab] = useState<'overview' | 'department' | 'trends' | 'items'>('overview');

  // 模拟统计数据
  const statsData: StatisticsData = {
    totalConsumption: 125680.50,
    totalOrders: 3420,
    avgOrderValue: 36.75,
    activeUsers: 156,
    consumptionGrowth: 12.5,
    orderGrowth: 8.3
  };

  // 模拟部门统计数据
  const departmentStats: DepartmentStats[] = [
    { department: '技术部', consumption: 45680.20, orders: 1250, avgPerPerson: 380.67, growth: 15.2 },
    { department: '产品部', consumption: 32450.80, orders: 890, avgPerPerson: 405.63, growth: 8.7 },
    { department: '设计部', consumption: 28920.30, orders: 780, avgPerPerson: 361.50, growth: 12.1 },
    { department: '运营部', consumption: 18629.20, orders: 500, avgPerPerson: 310.49, growth: -2.3 }
  ];

  // 模拟每日消费数据
  const dailyConsumption: DailyConsumption[] = [
    { date: '2024-12-09', consumption: 4250.80, orders: 115, users: 89 },
    { date: '2024-12-10', consumption: 4680.20, orders: 128, users: 95 },
    { date: '2024-12-11', consumption: 5120.50, orders: 142, users: 102 },
    { date: '2024-12-12', consumption: 4890.30, orders: 135, users: 98 },
    { date: '2024-12-13', consumption: 5340.70, orders: 148, users: 108 },
    { date: '2024-12-14', consumption: 3920.40, orders: 98, users: 76 },
    { date: '2024-12-15', consumption: 2180.60, orders: 52, users: 41 }
  ];

  // 模拟热门商品数据
  const popularItems: PopularItem[] = [
    { name: '宫保鸡丁套餐', category: '中式套餐', orders: 245, revenue: 6125.00, growth: 18.5 },
    { name: '番茄鸡蛋面', category: '面食', orders: 198, revenue: 3960.00, growth: 12.3 },
    { name: '红烧肉套餐', category: '中式套餐', orders: 176, revenue: 5280.00, growth: 8.7 },
    { name: '蔬菜沙拉', category: '轻食', orders: 156, revenue: 2340.00, growth: 25.6 },
    { name: '牛肉汉堡', category: '西式快餐', orders: 134, revenue: 4020.00, growth: -5.2 }
  ];

  const getTimeRangeText = () => {
    switch (timeRange) {
      case '7d': return '近7天';
      case '30d': return '近30天';
      case '90d': return '近90天';
      case '1y': return '近1年';
      default: return '近30天';
    }
  };

  const exportData = () => {
    toast.success('数据导出成功');
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">消费统计</h1>
          <p className="text-gray-600 mt-1">企业员工消费数据分析</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">近7天</option>
            <option value="30d">近30天</option>
            <option value="90d">近90天</option>
            <option value="1y">近1年</option>
          </select>
          <button
            onClick={exportData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>导出数据</span>
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总消费金额</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                ¥{statsData.totalConsumption.toLocaleString()}
              </p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+{statsData.consumptionGrowth}%</span>
                <span className="text-sm text-gray-500 ml-1">vs {getTimeRangeText()}</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总订单数</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {statsData.totalOrders.toLocaleString()}
              </p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+{statsData.orderGrowth}%</span>
                <span className="text-sm text-gray-500 ml-1">vs {getTimeRangeText()}</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <ShoppingCart className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均订单金额</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                ¥{statsData.avgOrderValue}
              </p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+3.2%</span>
                <span className="text-sm text-gray-500 ml-1">vs {getTimeRangeText()}</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">活跃用户</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {statsData.activeUsers}
              </p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+5.8%</span>
                <span className="text-sm text-gray-500 ml-1">vs {getTimeRangeText()}</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* 标签页 */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              数据概览
            </button>
            <button
              onClick={() => setActiveTab('department')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'department'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              部门统计
            </button>
            <button
              onClick={() => setActiveTab('trends')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'trends'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              消费趋势
            </button>
            <button
              onClick={() => setActiveTab('items')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'items'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              热门商品
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* 消费分布饼图 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">部门消费分布</h3>
                  <div className="space-y-4">
                    {departmentStats.map((dept, index) => {
                      const percentage = (dept.consumption / statsData.totalConsumption) * 100;
                      const colors = ['bg-blue-500', 'bg-green-500', 'bg-orange-500', 'bg-purple-500'];
                      return (
                        <div key={dept.department} className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`w-4 h-4 rounded ${colors[index % colors.length]}`}></div>
                            <span className="text-sm font-medium text-gray-700">{dept.department}</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">
                              ¥{dept.consumption.toLocaleString()}
                            </p>
                            <p className="text-xs text-gray-500">{percentage.toFixed(1)}%</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">消费时段分布</h3>
                  <div className="space-y-4">
                    {[
                      { time: '早餐 (07:00-10:00)', amount: 18500, percentage: 14.7 },
                      { time: '午餐 (11:00-14:00)', amount: 75200, percentage: 59.8 },
                      { time: '下午茶 (14:00-17:00)', amount: 12600, percentage: 10.0 },
                      { time: '晚餐 (17:00-20:00)', amount: 19380, percentage: 15.4 }
                    ].map((period, index) => {
                      const colors = ['bg-yellow-500', 'bg-red-500', 'bg-green-500', 'bg-blue-500'];
                      return (
                        <div key={period.time} className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`w-4 h-4 rounded ${colors[index]}`}></div>
                            <span className="text-sm font-medium text-gray-700">{period.time}</span>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">
                              ¥{period.amount.toLocaleString()}
                            </p>
                            <p className="text-xs text-gray-500">{period.percentage}%</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* 关键指标 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100">人均消费</p>
                      <p className="text-2xl font-bold mt-1">
                        ¥{(statsData.totalConsumption / statsData.activeUsers).toFixed(2)}
                      </p>
                      <p className="text-blue-100 text-sm mt-1">{getTimeRangeText()}</p>
                    </div>
                    <Activity className="w-8 h-8 text-blue-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100">订单完成率</p>
                      <p className="text-2xl font-bold mt-1">96.8%</p>
                      <p className="text-green-100 text-sm mt-1">较上期 +2.1%</p>
                    </div>
                    <TrendingUp className="w-8 h-8 text-green-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-100">复购率</p>
                      <p className="text-2xl font-bold mt-1">78.5%</p>
                      <p className="text-orange-100 text-sm mt-1">较上期 +5.3%</p>
                    </div>
                    <Users className="w-8 h-8 text-orange-200" />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'department' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">部门消费统计</h3>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        部门
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        总消费
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        订单数
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        人均消费
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        增长率
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        占比
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {departmentStats.map((dept) => {
                      const percentage = (dept.consumption / statsData.totalConsumption) * 100;
                      return (
                        <tr key={dept.department} className="hover:bg-gray-50">
                          <td className="px-4 py-4 text-sm font-medium text-gray-900">
                            {dept.department}
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            ¥{dept.consumption.toLocaleString()}
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            {dept.orders.toLocaleString()}
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            ¥{dept.avgPerPerson.toFixed(2)}
                          </td>
                          <td className="px-4 py-4 text-sm">
                            <div className={`flex items-center ${
                              dept.growth >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {dept.growth >= 0 ? (
                                <TrendingUp className="w-4 h-4 mr-1" />
                              ) : (
                                <TrendingDown className="w-4 h-4 mr-1" />
                              )}
                              {dept.growth >= 0 ? '+' : ''}{dept.growth}%
                            </div>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            <div className="flex items-center space-x-2">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-blue-600 h-2 rounded-full" 
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                              <span>{percentage.toFixed(1)}%</span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'trends' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">消费趋势分析</h3>
              
              {/* 趋势图表 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">每日消费趋势</h4>
                <div className="space-y-4">
                  {dailyConsumption.map((data, index) => {
                    const maxConsumption = Math.max(...dailyConsumption.map(d => d.consumption));
                    const width = (data.consumption / maxConsumption) * 100;
                    return (
                      <div key={index} className="flex items-center space-x-4">
                        <span className="text-sm text-gray-600 w-20">
                          {new Date(data.date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                        </span>
                        <div className="flex-1">
                          <div className="w-full bg-gray-200 rounded-full h-4">
                            <div 
                              className="bg-gradient-to-r from-blue-500 to-purple-600 h-4 rounded-full flex items-center justify-end pr-2" 
                              style={{ width: `${width}%` }}
                            >
                              <span className="text-xs text-white font-medium">
                                ¥{data.consumption.toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right w-24">
                          <p className="text-sm text-gray-900">{data.orders} 单</p>
                          <p className="text-xs text-gray-500">{data.users} 人</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* 趋势分析 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white border rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">消费高峰时段</h4>
                  <div className="space-y-3">
                    {[
                      { time: '12:00-13:00', percentage: 35.2, orders: 1205 },
                      { time: '18:00-19:00', percentage: 28.6, orders: 978 },
                      { time: '11:30-12:00', percentage: 18.4, orders: 629 },
                      { time: '19:00-20:00', percentage: 12.8, orders: 438 }
                    ].map((peak, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">{peak.time}</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${peak.percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900 w-16">{peak.orders} 单</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-white border rounded-lg p-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">周消费分布</h4>
                  <div className="space-y-3">
                    {[
                      { day: '周一', consumption: 18500, growth: 5.2 },
                      { day: '周二', consumption: 19200, growth: 8.1 },
                      { day: '周三', consumption: 20100, growth: 12.3 },
                      { day: '周四', consumption: 19800, growth: 9.7 },
                      { day: '周五', consumption: 21500, growth: 15.6 },
                      { day: '周六', consumption: 8900, growth: -12.4 },
                      { day: '周日', consumption: 6800, growth: -18.2 }
                    ].map((day, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm text-gray-700 w-8">{day.day}</span>
                        <div className="flex-1 mx-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ width: `${(day.consumption / 21500) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                        <div className="text-right w-20">
                          <p className="text-sm text-gray-900">¥{day.consumption.toLocaleString()}</p>
                          <p className={`text-xs ${
                            day.growth >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {day.growth >= 0 ? '+' : ''}{day.growth}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'items' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">热门商品统计</h3>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        排名
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        商品名称
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        分类
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        订单数
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        销售额
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        增长率
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {popularItems.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-4 text-sm text-gray-900">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                            <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm font-medium text-gray-900">
                          {item.name}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {item.category}
                          </span>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {item.orders.toLocaleString()}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          ¥{item.revenue.toLocaleString()}
                        </td>
                        <td className="px-4 py-4 text-sm">
                          <div className={`flex items-center ${
                            item.growth >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {item.growth >= 0 ? (
                              <TrendingUp className="w-4 h-4 mr-1" />
                            ) : (
                              <TrendingDown className="w-4 h-4 mr-1" />
                            )}
                            {item.growth >= 0 ? '+' : ''}{item.growth}%
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 商品分类统计 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">商品分类销售占比</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[
                    { category: '中式套餐', percentage: 42.5, revenue: 53400 },
                    { category: '面食', percentage: 18.3, revenue: 23000 },
                    { category: '轻食', percentage: 15.2, revenue: 19100 },
                    { category: '西式快餐', percentage: 12.8, revenue: 16100 },
                    { category: '饮品', percentage: 8.7, revenue: 10900 },
                    { category: '其他', percentage: 2.5, revenue: 3200 }
                  ].map((cat, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-4 h-4 rounded ${
                          ['bg-blue-500', 'bg-green-500', 'bg-orange-500', 'bg-purple-500', 'bg-red-500', 'bg-gray-500'][index]
                        }`}></div>
                        <span className="text-sm font-medium text-gray-700">{cat.category}</span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          ¥{cat.revenue.toLocaleString()}
                        </p>
                        <p className="text-xs text-gray-500">{cat.percentage}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Statistics;