import { useState } from 'react';
import { 
  Users, 
  CreditCard, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  ShoppingBag,
  Calendar,
  Clock,
  BarChart3,
  PieChart
} from 'lucide-react';

const Dashboard = () => {
  const [timeRange, setTimeRange] = useState('week');

  // 模拟数据
  const stats = {
    totalEmployees: 156,
    activeEmployees: 142,
    totalOrders: 1248,
    totalSpending: 28650.50,
    mealAllowanceUsed: 22100.00,
    personalSpending: 6550.50,
    avgOrderValue: 22.95
  };

  const recentOrders = [
    { id: '1', employee: '张三', amount: 25.50, time: '12:30', status: 'completed' },
    { id: '2', employee: '李四', amount: 18.80, time: '12:25', status: 'preparing' },
    { id: '3', employee: '王五', amount: 32.00, time: '12:20', status: 'completed' },
    { id: '4', employee: '赵六', amount: 15.60, time: '12:15', status: 'completed' },
    { id: '5', employee: '钱七', amount: 28.90, time: '12:10', status: 'completed' }
  ];

  const topEmployees = [
    { name: '张三', orders: 45, spending: 980.50 },
    { name: '李四', orders: 42, spending: 925.80 },
    { name: '王五', orders: 38, spending: 856.20 },
    { name: '赵六', orders: 35, spending: 798.60 },
    { name: '钱七', orders: 33, spending: 745.30 }
  ];

  const weeklyData = [
    { day: '周一', orders: 45, amount: 1025.50 },
    { day: '周二', orders: 52, amount: 1180.80 },
    { day: '周三', orders: 48, amount: 1095.20 },
    { day: '周四', orders: 55, amount: 1250.60 },
    { day: '周五', orders: 62, amount: 1420.30 },
    { day: '周六', orders: 28, amount: 650.40 },
    { day: '周日', orders: 15, amount: 380.20 }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据概览</h1>
          <p className="text-gray-600 mt-1">企业餐饮消费数据统计</p>
        </div>
        <div className="flex items-center space-x-2">
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="today">今日</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="quarter">本季度</option>
          </select>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总员工数</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{stats.totalEmployees}</p>
              <p className="text-sm text-green-600 mt-1 flex items-center">
                <TrendingUp className="w-4 h-4 mr-1" />
                活跃员工 {stats.activeEmployees}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总订单数</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{stats.totalOrders}</p>
              <p className="text-sm text-green-600 mt-1 flex items-center">
                <TrendingUp className="w-4 h-4 mr-1" />
                +12.5% 较上周
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <ShoppingBag className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总消费金额</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">¥{stats.totalSpending.toLocaleString()}</p>
              <p className="text-sm text-green-600 mt-1 flex items-center">
                <TrendingUp className="w-4 h-4 mr-1" />
                +8.2% 较上周
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">平均订单金额</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">¥{stats.avgOrderValue}</p>
              <p className="text-sm text-red-600 mt-1 flex items-center">
                <TrendingDown className="w-4 h-4 mr-1" />
                -2.1% 较上周
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* 餐标使用情况 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">餐标使用情况</h3>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-600">餐标支付</span>
                <span className="text-sm font-bold text-blue-600">¥{stats.mealAllowanceUsed.toLocaleString()}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${(stats.mealAllowanceUsed / stats.totalSpending) * 100}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                占总消费 {((stats.mealAllowanceUsed / stats.totalSpending) * 100).toFixed(1)}%
              </p>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-600">个人支付</span>
                <span className="text-sm font-bold text-orange-600">¥{stats.personalSpending.toLocaleString()}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-orange-600 h-2 rounded-full" 
                  style={{ width: `${(stats.personalSpending / stats.totalSpending) * 100}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                占总消费 {((stats.personalSpending / stats.totalSpending) * 100).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">本周消费趋势</h3>
          <div className="space-y-3">
            {weeklyData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 w-12">{item.day}</span>
                <div className="flex-1 mx-3">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" 
                      style={{ width: `${(item.amount / 1500) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{item.orders}单</p>
                  <p className="text-xs text-gray-500">¥{item.amount}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 最近订单和热门员工 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">最近订单</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">查看全部</button>
          </div>
          <div className="space-y-3">
            {recentOrders.map((order) => (
              <div key={order.id} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">
                      {order.employee.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{order.employee}</p>
                    <p className="text-xs text-gray-500">{order.time}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">¥{order.amount}</p>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    order.status === 'completed' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {order.status === 'completed' ? '已完成' : '制作中'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">消费排行榜</h3>
            <button className="text-sm text-blue-600 hover:text-blue-700">查看全部</button>
          </div>
          <div className="space-y-3">
            {topEmployees.map((employee, index) => (
              <div key={index} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    index === 0 ? 'bg-yellow-100 text-yellow-800' :
                    index === 1 ? 'bg-gray-100 text-gray-800' :
                    index === 2 ? 'bg-orange-100 text-orange-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{employee.name}</p>
                    <p className="text-xs text-gray-500">{employee.orders}单</p>
                  </div>
                </div>
                <p className="text-sm font-medium text-gray-900">¥{employee.spending}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;