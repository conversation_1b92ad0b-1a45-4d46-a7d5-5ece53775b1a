import { useState } from 'react';
import { 
  CreditCard, 
  Plus, 
  Edit, 
  Trash2, 
  Calendar, 
  DollarSign,
  Users,
  TrendingUp,
  Settings,
  X,
  Check,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface MealAllowanceRule {
  id: string;
  name: string;
  amount: number;
  description: string;
  departments: string[];
  positions: string[];
  effectiveDate: string;
  expiryDate?: string;
  status: 'active' | 'inactive';
  employeeCount: number;
}

interface MealAllowanceUsage {
  date: string;
  totalAmount: number;
  usedAmount: number;
  employeeCount: number;
  avgUsage: number;
}

const MealAllowance = () => {
  const [activeTab, setActiveTab] = useState<'rules' | 'usage' | 'settings'>('rules');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingRule, setEditingRule] = useState<MealAllowanceRule | null>(null);

  // 模拟餐标规则数据
  const [rules, setRules] = useState<MealAllowanceRule[]>([
    {
      id: '1',
      name: '标准餐标',
      amount: 25.00,
      description: '适用于大部分员工的标准餐标配置',
      departments: ['技术部', '产品部', '设计部'],
      positions: ['工程师', '设计师', '产品经理'],
      effectiveDate: '2024-01-01',
      status: 'active',
      employeeCount: 120
    },
    {
      id: '2',
      name: '高级餐标',
      amount: 35.00,
      description: '适用于管理层和高级职位的餐标配置',
      departments: ['技术部', '产品部'],
      positions: ['技术总监', '产品总监', '高级工程师'],
      effectiveDate: '2024-01-01',
      status: 'active',
      employeeCount: 25
    },
    {
      id: '3',
      name: '实习生餐标',
      amount: 15.00,
      description: '适用于实习生的餐标配置',
      departments: ['技术部', '产品部', '设计部', '运营部'],
      positions: ['实习生'],
      effectiveDate: '2024-01-01',
      status: 'active',
      employeeCount: 15
    }
  ]);

  // 模拟使用情况数据
  const usageData: MealAllowanceUsage[] = [
    { date: '2024-12-09', totalAmount: 4000.00, usedAmount: 3200.50, employeeCount: 160, avgUsage: 20.00 },
    { date: '2024-12-10', totalAmount: 4000.00, usedAmount: 3450.80, employeeCount: 160, avgUsage: 21.57 },
    { date: '2024-12-11', totalAmount: 4000.00, usedAmount: 3680.20, employeeCount: 160, avgUsage: 23.00 },
    { date: '2024-12-12', totalAmount: 4000.00, usedAmount: 3520.60, employeeCount: 160, avgUsage: 22.00 },
    { date: '2024-12-13', totalAmount: 4000.00, usedAmount: 3780.90, employeeCount: 160, avgUsage: 23.63 },
    { date: '2024-12-14', totalAmount: 4000.00, usedAmount: 2890.40, employeeCount: 160, avgUsage: 18.07 },
    { date: '2024-12-15', totalAmount: 4000.00, usedAmount: 1250.30, employeeCount: 160, avgUsage: 7.81 }
  ];

  const departments = ['技术部', '产品部', '设计部', '运营部', '市场部', '人事部', '财务部'];
  const positions = ['工程师', '设计师', '产品经理', '技术总监', '产品总监', '高级工程师', '实习生'];

  const handleAddRule = (ruleData: Partial<MealAllowanceRule>) => {
    const newRule: MealAllowanceRule = {
      id: Date.now().toString(),
      name: ruleData.name || '',
      amount: ruleData.amount || 0,
      description: ruleData.description || '',
      departments: ruleData.departments || [],
      positions: ruleData.positions || [],
      effectiveDate: ruleData.effectiveDate || new Date().toISOString().split('T')[0],
      expiryDate: ruleData.expiryDate,
      status: 'active',
      employeeCount: 0
    };
    
    setRules([...rules, newRule]);
    setShowAddModal(false);
    toast.success('餐标规则添加成功');
  };

  const handleEditRule = (ruleData: MealAllowanceRule) => {
    setRules(rules.map(rule => 
      rule.id === ruleData.id ? ruleData : rule
    ));
    setEditingRule(null);
    toast.success('餐标规则更新成功');
  };

  const handleDeleteRule = (id: string) => {
    setRules(rules.filter(rule => rule.id !== id));
    toast.success('餐标规则删除成功');
  };

  const toggleRuleStatus = (id: string) => {
    setRules(rules.map(rule => 
      rule.id === id 
        ? { ...rule, status: rule.status === 'active' ? 'inactive' : 'active' }
        : rule
    ));
    toast.success('规则状态更新成功');
  };

  const totalBudget = rules.reduce((sum, rule) => sum + (rule.amount * rule.employeeCount), 0);
  const todayUsage = usageData[usageData.length - 1];
  const usageRate = todayUsage ? (todayUsage.usedAmount / todayUsage.totalAmount) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">餐标管理</h1>
        <p className="text-gray-600 mt-1">配置和管理员工餐标规则</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总预算</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">¥{totalBudget.toLocaleString()}</p>
              <p className="text-sm text-gray-500 mt-1">每日预算</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">今日使用</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                ¥{todayUsage?.usedAmount.toLocaleString() || '0'}
              </p>
              <p className="text-sm text-green-600 mt-1">
                使用率 {usageRate.toFixed(1)}%
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">活跃规则</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {rules.filter(rule => rule.status === 'active').length}
              </p>
              <p className="text-sm text-gray-500 mt-1">共 {rules.length} 条规则</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Settings className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">覆盖员工</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">
                {rules.reduce((sum, rule) => sum + rule.employeeCount, 0)}
              </p>
              <p className="text-sm text-gray-500 mt-1">人</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* 标签页 */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('rules')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'rules'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              餐标规则
            </button>
            <button
              onClick={() => setActiveTab('usage')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'usage'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              使用情况
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'settings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              全局设置
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'rules' && (
            <div className="space-y-6">
              {/* 操作栏 */}
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">餐标规则列表</h3>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>添加规则</span>
                </button>
              </div>

              {/* 规则列表 */}
              <div className="space-y-4">
                {rules.map((rule) => (
                  <div key={rule.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="text-lg font-medium text-gray-900">{rule.name}</h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            rule.status === 'active' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {rule.status === 'active' ? '启用' : '禁用'}
                          </span>
                          <span className="text-2xl font-bold text-blue-600">¥{rule.amount}</span>
                        </div>
                        <p className="text-gray-600 mt-1">{rule.description}</p>
                        <div className="flex items-center space-x-6 mt-3 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Users className="w-4 h-4" />
                            <span>{rule.employeeCount} 人</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-4 h-4" />
                            <span>生效日期: {rule.effectiveDate}</span>
                          </div>
                          {rule.expiryDate && (
                            <div className="flex items-center space-x-1">
                              <AlertCircle className="w-4 h-4" />
                              <span>到期日期: {rule.expiryDate}</span>
                            </div>
                          )}
                        </div>
                        <div className="mt-2">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">适用部门:</span> {rule.departments.join(', ')}
                          </p>
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">适用职位:</span> {rule.positions.join(', ')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => toggleRuleStatus(rule.id)}
                          className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                            rule.status === 'active'
                              ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              : 'bg-green-100 text-green-700 hover:bg-green-200'
                          }`}
                        >
                          {rule.status === 'active' ? '禁用' : '启用'}
                        </button>
                        <button
                          onClick={() => setEditingRule(rule)}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteRule(rule.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'usage' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">餐标使用情况</h3>
              
              {/* 使用趋势图表 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">近7天使用趋势</h4>
                <div className="space-y-3">
                  {usageData.map((data, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 w-20">
                        {new Date(data.date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                      </span>
                      <div className="flex-1 mx-4">
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div 
                            className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full" 
                            style={{ width: `${(data.usedAmount / data.totalAmount) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="text-right w-32">
                        <p className="text-sm font-medium text-gray-900">
                          ¥{data.usedAmount} / ¥{data.totalAmount}
                        </p>
                        <p className="text-xs text-gray-500">
                          {((data.usedAmount / data.totalAmount) * 100).toFixed(1)}% 使用率
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 详细统计 */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        日期
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        总预算
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        已使用
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        使用率
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        人均消费
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        参与人数
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {usageData.map((data, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {new Date(data.date).toLocaleDateString('zh-CN')}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          ¥{data.totalAmount.toLocaleString()}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          ¥{data.usedAmount.toLocaleString()}
                        </td>
                        <td className="px-4 py-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${(data.usedAmount / data.totalAmount) * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-gray-900">
                              {((data.usedAmount / data.totalAmount) * 100).toFixed(1)}%
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          ¥{data.avgUsage.toFixed(2)}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {data.employeeCount}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">全局设置</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-gray-900">餐标配置</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      默认餐标金额 (元/天)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      defaultValue="25.00"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      餐标生效时间
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option>每日 00:00</option>
                      <option>每日 06:00</option>
                      <option>每日 08:00</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      餐标过期时间
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option>当日 23:59</option>
                      <option>次日 02:00</option>
                      <option>永不过期</option>
                    </select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-gray-900">使用规则</h4>
                  
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <span className="ml-2 text-sm text-gray-700">允许餐标累积</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <span className="ml-2 text-sm text-gray-700">允许超额消费</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <span className="ml-2 text-sm text-gray-700">周末餐标减半</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <span className="ml-2 text-sm text-gray-700">节假日餐标正常发放</span>
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-end space-x-3 pt-6 border-t">
                <button className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                  重置
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  保存设置
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 添加规则模态框 */}
      {showAddModal && (
        <RuleModal
          title="添加餐标规则"
          onSave={handleAddRule}
          onClose={() => setShowAddModal(false)}
          departments={departments}
          positions={positions}
        />
      )}

      {/* 编辑规则模态框 */}
      {editingRule && (
        <RuleModal
          title="编辑餐标规则"
          rule={editingRule}
          onSave={handleEditRule}
          onClose={() => setEditingRule(null)}
          departments={departments}
          positions={positions}
        />
      )}
    </div>
  );
};

// 规则模态框组件
interface RuleModalProps {
  title: string;
  rule?: MealAllowanceRule;
  onSave: (rule: any) => void;
  onClose: () => void;
  departments: string[];
  positions: string[];
}

const RuleModal = ({ title, rule, onSave, onClose, departments, positions }: RuleModalProps) => {
  const [formData, setFormData] = useState({
    name: rule?.name || '',
    amount: rule?.amount || 25.00,
    description: rule?.description || '',
    departments: rule?.departments || [],
    positions: rule?.positions || [],
    effectiveDate: rule?.effectiveDate || new Date().toISOString().split('T')[0],
    expiryDate: rule?.expiryDate || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (rule) {
      onSave({ ...rule, ...formData });
    } else {
      onSave(formData);
    }
  };

  const handleDepartmentChange = (dept: string) => {
    setFormData({
      ...formData,
      departments: formData.departments.includes(dept)
        ? formData.departments.filter(d => d !== dept)
        : [...formData.departments, dept]
    });
  };

  const handlePositionChange = (pos: string) => {
    setFormData({
      ...formData,
      positions: formData.positions.includes(pos)
        ? formData.positions.filter(p => p !== pos)
        : [...formData.positions, pos]
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                规则名称 *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                餐标金额 (元/天) *
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                required
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              规则描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                生效日期 *
              </label>
              <input
                type="date"
                required
                value={formData.effectiveDate}
                onChange={(e) => setFormData({ ...formData, effectiveDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                到期日期 (可选)
              </label>
              <input
                type="date"
                value={formData.expiryDate}
                onChange={(e) => setFormData({ ...formData, expiryDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              适用部门 *
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {departments.map(dept => (
                <label key={dept} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.departments.includes(dept)}
                    onChange={() => handleDepartmentChange(dept)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{dept}</span>
                </label>
              ))}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              适用职位 *
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {positions.map(pos => (
                <label key={pos} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.positions.includes(pos)}
                    onChange={() => handlePositionChange(pos)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{pos}</span>
                </label>
              ))}
            </div>
          </div>
          
          <div className="flex items-center justify-end space-x-3 pt-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Check className="w-4 h-4" />
              <span>保存</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MealAllowance;