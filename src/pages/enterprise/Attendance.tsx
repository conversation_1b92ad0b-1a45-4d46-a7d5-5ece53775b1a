import { useState } from 'react';
import { 
  Clock, 
  <PERSON>, 
  Settings, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RefreshCw, 
  Download, 
  Upload, 
  Calendar, 
  Database, 
  Wifi, 
  WifiOff,
  Play,
  Pause,
  RotateCcw,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';

interface AttendanceSystem {
  id: string;
  name: string;
  type: 'dingtalk' | 'wechat' | 'feishu' | 'custom';
  status: 'connected' | 'disconnected' | 'error';
  lastSync: string;
  employeeCount: number;
  config: {
    apiUrl?: string;
    appId?: string;
    appSecret?: string;
    syncInterval: number;
    autoSync: boolean;
  };
}

interface SyncRecord {
  id: string;
  timestamp: string;
  type: 'manual' | 'auto';
  status: 'success' | 'failed' | 'partial';
  employeesProcessed: number;
  errors?: string[];
  duration: number;
}

interface AttendanceRule {
  id: string;
  name: string;
  workDays: string[];
  workStartTime: string;
  workEndTime: string;
  lunchStartTime: string;
  lunchEndTime: string;
  allowLateMinutes: number;
  mealAllowanceEnabled: boolean;
  departments: string[];
}

const Attendance = () => {
  const [activeTab, setActiveTab] = useState<'systems' | 'sync' | 'rules' | 'logs'>('systems');
  const [showAddSystemModal, setShowAddSystemModal] = useState(false);
  const [showAddRuleModal, setShowAddRuleModal] = useState(false);
  const [editingSystem, setEditingSystem] = useState<AttendanceSystem | null>(null);
  const [editingRule, setEditingRule] = useState<AttendanceRule | null>(null);

  // 模拟考勤系统数据
  const [systems, setSystems] = useState<AttendanceSystem[]>([
    {
      id: '1',
      name: '钉钉考勤',
      type: 'dingtalk',
      status: 'connected',
      lastSync: '2024-12-15 09:30:00',
      employeeCount: 156,
      config: {
        appId: 'dingxxxxxxxxxxxxxxx',
        appSecret: '*********************',
        syncInterval: 60,
        autoSync: true
      }
    },
    {
      id: '2',
      name: '企业微信',
      type: 'wechat',
      status: 'disconnected',
      lastSync: '2024-12-14 18:00:00',
      employeeCount: 0,
      config: {
        appId: '',
        appSecret: '',
        syncInterval: 30,
        autoSync: false
      }
    }
  ]);

  // 模拟同步记录
  const syncRecords: SyncRecord[] = [
    {
      id: '1',
      timestamp: '2024-12-15 09:30:00',
      type: 'auto',
      status: 'success',
      employeesProcessed: 156,
      duration: 12
    },
    {
      id: '2',
      timestamp: '2024-12-15 08:30:00',
      type: 'auto',
      status: 'success',
      employeesProcessed: 156,
      duration: 15
    },
    {
      id: '3',
      timestamp: '2024-12-14 18:00:00',
      type: 'manual',
      status: 'partial',
      employeesProcessed: 142,
      errors: ['14名员工数据同步失败', 'API限流'],
      duration: 28
    }
  ];

  // 模拟考勤规则
  const [rules, setRules] = useState<AttendanceRule[]>([
    {
      id: '1',
      name: '标准工作制',
      workDays: ['周一', '周二', '周三', '周四', '周五'],
      workStartTime: '09:00',
      workEndTime: '18:00',
      lunchStartTime: '12:00',
      lunchEndTime: '13:00',
      allowLateMinutes: 15,
      mealAllowanceEnabled: true,
      departments: ['技术部', '产品部', '设计部']
    },
    {
      id: '2',
      name: '弹性工作制',
      workDays: ['周一', '周二', '周三', '周四', '周五'],
      workStartTime: '08:30',
      workEndTime: '17:30',
      lunchStartTime: '11:30',
      lunchEndTime: '12:30',
      allowLateMinutes: 30,
      mealAllowanceEnabled: true,
      departments: ['运营部', '市场部']
    }
  ]);

  const getSystemIcon = (type: string) => {
    switch (type) {
      case 'dingtalk': return '🔔';
      case 'wechat': return '💬';
      case 'feishu': return '🚀';
      default: return '⚙️';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-600 bg-green-100';
      case 'disconnected': return 'text-gray-600 bg-gray-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="w-4 h-4" />;
      case 'disconnected': return <XCircle className="w-4 h-4" />;
      case 'error': return <AlertCircle className="w-4 h-4" />;
      default: return <XCircle className="w-4 h-4" />;
    }
  };

  const handleSync = (systemId: string) => {
    toast.success('开始同步考勤数据...');
    // 模拟同步过程
    setTimeout(() => {
      toast.success('考勤数据同步完成');
    }, 2000);
  };

  const handleToggleAutoSync = (systemId: string) => {
    setSystems(systems.map(system => 
      system.id === systemId 
        ? { ...system, config: { ...system.config, autoSync: !system.config.autoSync } }
        : system
    ));
    toast.success('自动同步设置已更新');
  };

  const handleDeleteSystem = (systemId: string) => {
    setSystems(systems.filter(system => system.id !== systemId));
    toast.success('考勤系统已删除');
  };

  const handleDeleteRule = (ruleId: string) => {
    setRules(rules.filter(rule => rule.id !== ruleId));
    toast.success('考勤规则已删除');
  };

  const connectedSystems = systems.filter(s => s.status === 'connected').length;
  const totalEmployees = systems.reduce((sum, s) => sum + s.employeeCount, 0);
  const lastSyncTime = systems
    .filter(s => s.lastSync)
    .sort((a, b) => new Date(b.lastSync).getTime() - new Date(a.lastSync).getTime())[0]?.lastSync;

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">考勤对接</h1>
        <p className="text-gray-600 mt-1">管理企业考勤系统对接和数据同步</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">已连接系统</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{connectedSystems}</p>
              <p className="text-sm text-gray-500 mt-1">共 {systems.length} 个系统</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Database className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">同步员工数</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{totalEmployees}</p>
              <p className="text-sm text-green-600 mt-1">实时同步</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">最后同步</p>
              <p className="text-lg font-bold text-gray-900 mt-1">
                {lastSyncTime ? new Date(lastSyncTime).toLocaleTimeString('zh-CN', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                }) : '--:--'}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                {lastSyncTime ? new Date(lastSyncTime).toLocaleDateString('zh-CN') : '暂无数据'}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">考勤规则</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{rules.length}</p>
              <p className="text-sm text-gray-500 mt-1">活跃规则</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Settings className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* 标签页 */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('systems')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'systems'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              系统管理
            </button>
            <button
              onClick={() => setActiveTab('sync')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'sync'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              数据同步
            </button>
            <button
              onClick={() => setActiveTab('rules')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'rules'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              考勤规则
            </button>
            <button
              onClick={() => setActiveTab('logs')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'logs'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              同步日志
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'systems' && (
            <div className="space-y-6">
              {/* 操作栏 */}
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">考勤系统列表</h3>
                <button
                  onClick={() => setShowAddSystemModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Database className="w-4 h-4" />
                  <span>添加系统</span>
                </button>
              </div>

              {/* 系统列表 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {systems.map((system) => (
                  <div key={system.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">{getSystemIcon(system.type)}</div>
                        <div>
                          <h4 className="text-lg font-medium text-gray-900">{system.name}</h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              getStatusColor(system.status)
                            }`}>
                              {getStatusIcon(system.status)}
                              <span className="ml-1">
                                {system.status === 'connected' ? '已连接' : 
                                 system.status === 'disconnected' ? '未连接' : '连接错误'}
                              </span>
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setEditingSystem(system)}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteSystem(system.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div className="mt-4 space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">同步员工数:</span>
                        <span className="font-medium text-gray-900">{system.employeeCount} 人</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">最后同步:</span>
                        <span className="font-medium text-gray-900">
                          {new Date(system.lastSync).toLocaleString('zh-CN')}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">同步间隔:</span>
                        <span className="font-medium text-gray-900">{system.config.syncInterval} 分钟</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">自动同步:</span>
                        <button
                          onClick={() => handleToggleAutoSync(system.id)}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            system.config.autoSync ? 'bg-blue-600' : 'bg-gray-200'
                          }`}
                        >
                          <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            system.config.autoSync ? 'translate-x-6' : 'translate-x-1'
                          }`} />
                        </button>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t flex items-center space-x-3">
                      <button
                        onClick={() => handleSync(system.id)}
                        disabled={system.status !== 'connected'}
                        className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                      >
                        <RefreshCw className="w-4 h-4" />
                        <span>立即同步</span>
                      </button>
                      <button className="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                        <Eye className="w-4 h-4" />
                        <span>查看详情</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'sync' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">数据同步管理</h3>
                <div className="flex items-center space-x-3">
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>导出数据</span>
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
                    <RefreshCw className="w-4 h-4" />
                    <span>全量同步</span>
                  </button>
                </div>
              </div>

              {/* 同步状态 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-6 h-6 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-green-800">同步正常</p>
                      <p className="text-2xl font-bold text-green-900 mt-1">156</p>
                      <p className="text-sm text-green-600">员工数据已同步</p>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                      <AlertCircle className="w-6 h-6 text-yellow-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-yellow-800">待处理</p>
                      <p className="text-2xl font-bold text-yellow-900 mt-1">14</p>
                      <p className="text-sm text-yellow-600">员工数据异常</p>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Clock className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-800">下次同步</p>
                      <p className="text-lg font-bold text-blue-900 mt-1">10:30</p>
                      <p className="text-sm text-blue-600">自动同步</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 同步设置 */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">同步设置</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        同步频率
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>每30分钟</option>
                        <option>每小时</option>
                        <option>每2小时</option>
                        <option>每天</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        同步时间段
                      </label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="time"
                          defaultValue="08:00"
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <span className="text-gray-500">至</span>
                        <input
                          type="time"
                          defaultValue="20:00"
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                        <span className="ml-2 text-sm text-gray-700">启用自动同步</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                        <span className="ml-2 text-sm text-gray-700">同步失败时重试</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                        <span className="ml-2 text-sm text-gray-700">仅同步工作日</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                        <span className="ml-2 text-sm text-gray-700">发送同步通知</span>
                      </label>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-end space-x-3 mt-6">
                  <button className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                    重置
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    保存设置
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'rules' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">考勤规则配置</h3>
                <button
                  onClick={() => setShowAddRuleModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Settings className="w-4 h-4" />
                  <span>添加规则</span>
                </button>
              </div>

              <div className="space-y-4">
                {rules.map((rule) => (
                  <div key={rule.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-lg font-medium text-gray-900">{rule.name}</h4>
                        <div className="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">工作日:</span>
                            <span className="ml-2 text-gray-900">{rule.workDays.join(', ')}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">工作时间:</span>
                            <span className="ml-2 text-gray-900">{rule.workStartTime} - {rule.workEndTime}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">午休时间:</span>
                            <span className="ml-2 text-gray-900">{rule.lunchStartTime} - {rule.lunchEndTime}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">允许迟到:</span>
                            <span className="ml-2 text-gray-900">{rule.allowLateMinutes} 分钟</span>
                          </div>
                          <div>
                            <span className="text-gray-600">餐标发放:</span>
                            <span className={`ml-2 ${
                              rule.mealAllowanceEnabled ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {rule.mealAllowanceEnabled ? '启用' : '禁用'}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">适用部门:</span>
                            <span className="ml-2 text-gray-900">{rule.departments.join(', ')}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setEditingRule(rule)}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteRule(rule.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'logs' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">同步日志</h3>
                <div className="flex items-center space-x-3">
                  <select className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option>全部状态</option>
                    <option>成功</option>
                    <option>失败</option>
                    <option>部分成功</option>
                  </select>
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>导出日志</span>
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        时间
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        类型
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        处理员工数
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        耗时
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        详情
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {syncRecords.map((record) => (
                      <tr key={record.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {new Date(record.timestamp).toLocaleString('zh-CN')}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            record.type === 'auto' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {record.type === 'auto' ? '自动' : '手动'}
                          </span>
                        </td>
                        <td className="px-4 py-4 text-sm">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            record.status === 'success' ? 'bg-green-100 text-green-800' :
                            record.status === 'failed' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {record.status === 'success' ? '成功' :
                             record.status === 'failed' ? '失败' : '部分成功'}
                          </span>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {record.employeesProcessed}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {record.duration}s
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {record.errors && record.errors.length > 0 ? (
                            <button className="text-blue-600 hover:text-blue-800">
                              查看错误
                            </button>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Attendance;