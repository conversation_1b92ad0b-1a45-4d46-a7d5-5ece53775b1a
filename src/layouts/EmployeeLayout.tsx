import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Home, ShoppingCart, FileText, User, Bell } from 'lucide-react';
import { useCartStore, useNotificationStore } from '../store';
import { toast, Toaster } from 'sonner';

const EmployeeLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { totalItems } = useCartStore();
  const { unreadCount } = useNotificationStore();

  const navItems = [
    {
      path: '/employee',
      icon: Home,
      label: '首页',
      exact: true
    },
    {
      path: '/employee/cart',
      icon: ShoppingCart,
      label: '购物车',
      badge: totalItems > 0 ? totalItems : undefined
    },
    {
      path: '/employee/orders',
      icon: FileText,
      label: '订单'
    },
    {
      path: '/employee/profile',
      icon: User,
      label: '我的'
    }
  ];

  const isActive = (path: string, exact?: boolean) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 顶部状态栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
            <Home className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">企业餐饮</h1>
            <p className="text-xs text-gray-500">智能点餐服务</p>
          </div>
        </div>
        
        {/* 通知图标 */}
        <button 
          onClick={() => toast.info('通知功能开发中')}
          className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
        >
          <Bell className="w-6 h-6" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </button>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-auto pb-20">
        <Outlet />
      </div>

      {/* 底部导航栏 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-around">
          {navItems.map((item) => {
            const IconComponent = item.icon;
            const active = isActive(item.path, item.exact);
            
            return (
              <button
                key={item.path}
                onClick={() => navigate(item.path)}
                className={`flex flex-col items-center space-y-1 py-2 px-3 rounded-lg transition-all duration-200 relative ${
                  active 
                    ? 'text-orange-500 bg-orange-50' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <div className="relative">
                  <IconComponent className={`w-6 h-6 ${
                    active ? 'text-orange-500' : 'text-gray-600'
                  }`} />
                  {item.badge && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}
                </div>
                <span className={`text-xs font-medium ${
                  active ? 'text-orange-500' : 'text-gray-600'
                }`}>
                  {item.label}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Toast 通知 */}
      <Toaster 
        position="top-center"
        toastOptions={{
          style: {
            background: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '12px',
            padding: '12px 16px'
          }
        }}
      />
    </div>
  );
};

export default EmployeeLayout;