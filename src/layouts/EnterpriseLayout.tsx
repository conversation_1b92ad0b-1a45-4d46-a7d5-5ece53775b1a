import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { 
  Users, 
  CreditCard, 
  BarChart3, 
  Clock, 
  Settings, 
  LogOut,
  Menu,
  X,
  Bell,
  Search
} from 'lucide-react';
import { useState } from 'react';
import { useUserStore } from '../store';
import { toast } from 'sonner';

const EnterpriseLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useUserStore();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const menuItems = [
    { path: '/enterprise', icon: BarChart3, label: '数据概览', exact: true },
    { path: '/enterprise/employees', icon: Users, label: '员工管理' },
    { path: '/enterprise/meal-allowance', icon: CreditCard, label: '餐标管理' },
    { path: '/enterprise/statistics', icon: BarChart3, label: '消费统计' },
    { path: '/enterprise/attendance', icon: Clock, label: '考勤对接' },
    { path: '/enterprise/settings', icon: Settings, label: '企业设置' },
  ];

  const handleLogout = () => {
    logout();
    toast.success('已退出登录');
    navigate('/auth/login');
  };

  const isActive = (path: string, exact = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 移动端顶部栏 */}
      <div className="lg:hidden bg-white shadow-sm border-b px-4 py-3 flex items-center justify-between">
        <button
          onClick={() => setSidebarOpen(true)}
          className="p-2 rounded-md text-gray-600 hover:bg-gray-100"
        >
          <Menu className="w-6 h-6" />
        </button>
        <h1 className="text-lg font-semibold text-gray-900">企业管理后台</h1>
        <div className="flex items-center space-x-2">
          <button className="p-2 rounded-md text-gray-600 hover:bg-gray-100">
            <Bell className="w-5 h-5" />
          </button>
          <button className="p-2 rounded-md text-gray-600 hover:bg-gray-100">
            <Search className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 侧边栏遮罩 */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 主要布局容器 */}
      <div className="lg:flex">
        {/* 侧边栏 */}
        <div className={`
          fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
          lg:translate-x-0 lg:relative lg:z-auto lg:flex-shrink-0
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">企</span>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">企业管理</h2>
              <p className="text-xs text-gray-500">{user?.name || '管理员'}</p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-md text-gray-400 hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.path, item.exact);
            
            return (
              <button
                key={item.path}
                onClick={() => {
                  navigate(item.path);
                  setSidebarOpen(false);
                }}
                className={`
                  w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-colors
                  ${active 
                    ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                    : 'text-gray-700 hover:bg-gray-100'
                  }
                `}
              >
                <Icon className={`w-5 h-5 ${active ? 'text-blue-600' : 'text-gray-500'}`} />
                <span className="font-medium">{item.label}</span>
              </button>
            );
          })}
        </nav>

        {/* 底部用户信息 */}
        <div className="border-t p-4">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-600 font-medium text-sm">
                {user?.name?.charAt(0) || 'A'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.name || '企业管理员'}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user?.email || '<EMAIL>'}
              </p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <LogOut className="w-4 h-4" />
            <span>退出登录</span>
          </button>
        </div>
        </div>

        {/* 主内容区域 */}
        <div className="flex-1 lg:flex lg:flex-col">
          {/* 桌面端顶部栏 */}
          <div className="hidden lg:block bg-white shadow-sm border-b px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">企业管理后台</h1>
                <p className="text-sm text-gray-600 mt-1">管理企业员工、餐标配置和消费数据</p>
              </div>
              <div className="flex items-center space-x-4">
                <button className="p-2 rounded-lg text-gray-600 hover:bg-gray-100">
                  <Search className="w-5 h-5" />
                </button>
                <button className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 relative">
                  <Bell className="w-5 h-5" />
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                </button>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-gray-600 font-medium text-sm">
                      {user?.name?.charAt(0) || 'A'}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    {user?.name || '企业管理员'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 页面内容 */}
          <main className="flex-1 p-4 lg:p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
};

export default EnterpriseLayout;