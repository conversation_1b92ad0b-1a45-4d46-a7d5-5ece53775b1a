import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Package, FileText, BarChart3, Settings, LogOut, Bell, User } from 'lucide-react';
import { useUserStore } from '../store';
import { toast, Toaster } from 'sonner';

const MerchantLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useUserStore();

  const menuItems = [
    { path: '/merchant/dashboard', icon: BarChart3, label: '数据概览' },
    { path: '/merchant/products', icon: Package, label: '商品管理' },
    { path: '/merchant/orders', icon: FileText, label: '订单管理' },
    { path: '/merchant/settings', icon: Settings, label: '店铺设置' },
  ];

  const handleLogout = () => {
    logout();
    toast.success('已退出登录');
    navigate('/auth/login');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-center" richColors />
      
      {/* 顶部导航栏 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">商户管理后台</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-500">
                <Bell className="h-5 w-5" />
              </button>
              
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-700">{user?.name || '商户用户'}</span>
                </div>
                
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700"
                >
                  <LogOut className="h-4 w-4" />
                  <span>退出</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* 侧边导航 */}
        <nav className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-4">
            <ul className="space-y-2">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;
                
                return (
                  <li key={item.path}>
                    <button
                      onClick={() => navigate(item.path)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        isActive
                          ? 'bg-orange-50 text-orange-600 border-r-2 border-orange-600'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="font-medium">{item.label}</span>
                    </button>
                  </li>
                );
              })}
            </ul>
          </div>
        </nav>

        {/* 主内容区域 */}
        <main className="flex-1 p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default MerchantLayout;